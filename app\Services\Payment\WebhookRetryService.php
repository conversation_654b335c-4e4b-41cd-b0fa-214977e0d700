<?php

namespace App\Services\Payment;

use App\Models\WebhookLog;
use App\Models\PaymentGateway;
use App\Services\Payment\PaymentGatewayManager;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Http\Request;
use Carbon\Carbon;

class WebhookRetryService
{
    /**
     * Maximum retry attempts
     */
    private const MAX_RETRY_ATTEMPTS = 5;

    /**
     * Base delay in seconds for exponential backoff
     */
    private const BASE_DELAY = 60;

    /**
     * Maximum delay in seconds
     */
    private const MAX_DELAY = 3600; // 1 hour

    protected PaymentGatewayManager $gatewayManager;

    public function __construct(PaymentGatewayManager $gatewayManager)
    {
        $this->gatewayManager = $gatewayManager;
    }

    /**
     * Process webhook with retry logic
     */
    public function processWebhookWithRetry(WebhookLog $webhookLog): bool
    {
        try {
            // Check if we've exceeded max retry attempts
            if ($webhookLog->retry_count >= self::MAX_RETRY_ATTEMPTS) {
                $this->markWebhookAsFinallyFailed($webhookLog);
                return false;
            }

            // Increment retry count
            $webhookLog->increment('retry_count');

            // Get the gateway service
            $gateway = $webhookLog->gateway;
            if (!$gateway || !$gateway->is_active) {
                throw new \Exception('Gateway not found or inactive');
            }

            $gatewayService = $this->gatewayManager->getGatewayService($gateway->name);

            // Recreate the request from stored payload
            $request = $this->recreateRequest($webhookLog);

            // Process the webhook
            $response = $gatewayService->handleWebhook($request);

            if ($response->success) {
                // Mark as processed
                $webhookLog->update([
                    'status' => 'processed',
                    'processed_at' => now(),
                    'error_message' => null
                ]);

                Log::info('Webhook retry successful', [
                    'webhook_log_id' => $webhookLog->id,
                    'retry_count' => $webhookLog->retry_count,
                    'gateway' => $gateway->name
                ]);

                return true;
            } else {
                throw new \Exception($response->message ?? 'Webhook processing failed');
            }

        } catch (\Exception $e) {
            // Update error information
            $webhookLog->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);

            Log::error('Webhook retry failed', [
                'webhook_log_id' => $webhookLog->id,
                'retry_count' => $webhookLog->retry_count,
                'error' => $e->getMessage(),
                'gateway' => $webhookLog->gateway->name ?? 'unknown'
            ]);

            // Schedule next retry if we haven't exceeded max attempts
            if ($webhookLog->retry_count < self::MAX_RETRY_ATTEMPTS) {
                $this->scheduleRetry($webhookLog);
            } else {
                $this->markWebhookAsFinallyFailed($webhookLog);
            }

            return false;
        }
    }

    /**
     * Schedule webhook retry with exponential backoff
     */
    public function scheduleRetry(WebhookLog $webhookLog): void
    {
        $delay = $this->calculateRetryDelay($webhookLog->retry_count);
        
        // Schedule the retry job
        Queue::later(
            now()->addSeconds($delay),
            new \App\Jobs\RetryWebhookJob($webhookLog->id)
        );

        Log::info('Webhook retry scheduled', [
            'webhook_log_id' => $webhookLog->id,
            'retry_count' => $webhookLog->retry_count,
            'delay_seconds' => $delay,
            'scheduled_for' => now()->addSeconds($delay)->toISOString()
        ]);
    }

    /**
     * Calculate retry delay using exponential backoff
     */
    private function calculateRetryDelay(int $retryCount): int
    {
        // Exponential backoff: base_delay * (2 ^ retry_count) with jitter
        $delay = self::BASE_DELAY * pow(2, $retryCount);
        
        // Add jitter (±25% randomization)
        $jitter = $delay * 0.25;
        $delay = $delay + rand(-$jitter, $jitter);
        
        // Cap at maximum delay
        return min($delay, self::MAX_DELAY);
    }

    /**
     * Mark webhook as finally failed after all retries
     */
    private function markWebhookAsFinallyFailed(WebhookLog $webhookLog): void
    {
        $webhookLog->update([
            'status' => 'failed',
            'error_message' => 'Maximum retry attempts exceeded'
        ]);

        // Send notification about final failure
        $this->notifyWebhookFinalFailure($webhookLog);

        Log::error('Webhook finally failed after all retries', [
            'webhook_log_id' => $webhookLog->id,
            'retry_count' => $webhookLog->retry_count,
            'gateway' => $webhookLog->gateway->name ?? 'unknown',
            'event_type' => $webhookLog->event_type
        ]);
    }

    /**
     * Recreate HTTP request from webhook log
     */
    private function recreateRequest(WebhookLog $webhookLog): Request
    {
        $payload = $webhookLog->payload;
        $headers = [];

        // Add gateway-specific headers
        if ($webhookLog->signature) {
            switch ($webhookLog->gateway->name) {
                case 'razorpay':
                    $headers['X-Razorpay-Signature'] = $webhookLog->signature;
                    break;
                case 'paypal':
                    $headers['PAYPAL-TRANSMISSION-SIG'] = $webhookLog->signature;
                    break;
            }
        }

        $headers['Content-Type'] = 'application/json';

        // Create request
        $request = Request::create(
            '/webhook/' . $webhookLog->gateway->name,
            'POST',
            [],
            [],
            [],
            $headers,
            json_encode($payload)
        );

        return $request;
    }

    /**
     * Manually retry a webhook (admin function)
     */
    public function manualRetry(WebhookLog $webhookLog): array
    {
        try {
            // Reset retry count for manual retry
            $originalRetryCount = $webhookLog->retry_count;
            $webhookLog->update([
                'retry_count' => 0,
                'status' => 'pending'
            ]);

            $success = $this->processWebhookWithRetry($webhookLog);

            return [
                'success' => $success,
                'message' => $success ? 'Webhook processed successfully' : 'Webhook processing failed',
                'original_retry_count' => $originalRetryCount,
                'new_retry_count' => $webhookLog->fresh()->retry_count
            ];

        } catch (\Exception $e) {
            Log::error('Manual webhook retry failed', [
                'webhook_log_id' => $webhookLog->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Manual retry failed: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Bulk retry failed webhooks
     */
    public function bulkRetryFailed(array $filters = []): array
    {
        $query = WebhookLog::where('status', 'failed')
                           ->where('retry_count', '<', self::MAX_RETRY_ATTEMPTS);

        // Apply filters
        if (isset($filters['gateway_id'])) {
            $query->where('gateway_id', $filters['gateway_id']);
        }

        if (isset($filters['event_type'])) {
            $query->where('event_type', $filters['event_type']);
        }

        if (isset($filters['created_after'])) {
            $query->where('created_at', '>=', $filters['created_after']);
        }

        $failedWebhooks = $query->limit(100)->get(); // Limit to prevent overwhelming

        $results = [
            'total_processed' => 0,
            'successful_retries' => 0,
            'failed_retries' => 0,
            'details' => []
        ];

        foreach ($failedWebhooks as $webhook) {
            $result = $this->manualRetry($webhook);
            $results['total_processed']++;
            
            if ($result['success']) {
                $results['successful_retries']++;
            } else {
                $results['failed_retries']++;
            }

            $results['details'][] = [
                'webhook_id' => $webhook->id,
                'success' => $result['success'],
                'message' => $result['message']
            ];
        }

        Log::info('Bulk webhook retry completed', $results);

        return $results;
    }

    /**
     * Get retry statistics
     */
    public function getRetryStatistics(array $filters = []): array
    {
        $query = WebhookLog::query();

        // Apply filters
        if (isset($filters['gateway_id'])) {
            $query->where('gateway_id', $filters['gateway_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $stats = [
            'total_webhooks' => $query->count(),
            'by_status' => [
                'processed' => $query->clone()->where('status', 'processed')->count(),
                'failed' => $query->clone()->where('status', 'failed')->count(),
                'pending' => $query->clone()->where('status', 'pending')->count(),
            ],
            'retry_distribution' => [],
            'average_retry_count' => 0,
            'success_rate_after_retry' => 0
        ];

        // Get retry count distribution
        for ($i = 0; $i <= self::MAX_RETRY_ATTEMPTS; $i++) {
            $count = $query->clone()->where('retry_count', $i)->count();
            $stats['retry_distribution'][$i] = $count;
        }

        // Calculate average retry count
        $avgRetryCount = $query->clone()->avg('retry_count');
        $stats['average_retry_count'] = round($avgRetryCount, 2);

        // Calculate success rate after retry
        $retriedWebhooks = $query->clone()->where('retry_count', '>', 0)->count();
        $successfulAfterRetry = $query->clone()
                                     ->where('retry_count', '>', 0)
                                     ->where('status', 'processed')
                                     ->count();

        if ($retriedWebhooks > 0) {
            $stats['success_rate_after_retry'] = round(($successfulAfterRetry / $retriedWebhooks) * 100, 2);
        }

        return $stats;
    }

    /**
     * Clean up old webhook logs
     */
    public function cleanupOldWebhooks(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deletedCount = WebhookLog::where('created_at', '<', $cutoffDate)
                                  ->where('status', '!=', 'pending') // Don't delete pending webhooks
                                  ->delete();

        Log::info('Old webhook logs cleaned up', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate->toISOString()
        ]);

        return $deletedCount;
    }

    /**
     * Send notification about webhook final failure
     */
    private function notifyWebhookFinalFailure(WebhookLog $webhookLog): void
    {
        $notificationData = [
            'webhook_id' => $webhookLog->id,
            'gateway_name' => $webhookLog->gateway->name ?? 'unknown',
            'event_type' => $webhookLog->event_type,
            'retry_count' => $webhookLog->retry_count,
            'error_message' => $webhookLog->error_message,
            'created_at' => $webhookLog->created_at->toISOString(),
            'payload' => $webhookLog->payload
        ];

        // Log to admin alerts
        Log::channel('admin-alerts')->error('Webhook finally failed after all retries', $notificationData);

        // In a real implementation, send notifications to administrators
        // Examples: Email, Slack, SMS, etc.
    }

    /**
     * Get webhook health status
     */
    public function getWebhookHealthStatus(): array
    {
        $last24Hours = now()->subHours(24);
        
        $health = [
            'overall_status' => 'healthy',
            'last_24_hours' => [
                'total_webhooks' => 0,
                'successful' => 0,
                'failed' => 0,
                'pending' => 0,
                'success_rate' => 0
            ],
            'by_gateway' => [],
            'alerts' => []
        ];

        // Get overall stats for last 24 hours
        $recentWebhooks = WebhookLog::where('created_at', '>=', $last24Hours);
        $health['last_24_hours']['total_webhooks'] = $recentWebhooks->count();
        $health['last_24_hours']['successful'] = $recentWebhooks->clone()->where('status', 'processed')->count();
        $health['last_24_hours']['failed'] = $recentWebhooks->clone()->where('status', 'failed')->count();
        $health['last_24_hours']['pending'] = $recentWebhooks->clone()->where('status', 'pending')->count();

        if ($health['last_24_hours']['total_webhooks'] > 0) {
            $health['last_24_hours']['success_rate'] = round(
                ($health['last_24_hours']['successful'] / $health['last_24_hours']['total_webhooks']) * 100,
                2
            );
        }

        // Get stats by gateway
        $gateways = PaymentGateway::where('is_active', true)->get();
        foreach ($gateways as $gateway) {
            $gatewayWebhooks = $recentWebhooks->clone()->where('gateway_id', $gateway->id);
            $total = $gatewayWebhooks->count();
            $successful = $gatewayWebhooks->clone()->where('status', 'processed')->count();
            $failed = $gatewayWebhooks->clone()->where('status', 'failed')->count();

            $successRate = $total > 0 ? round(($successful / $total) * 100, 2) : 100;

            $health['by_gateway'][$gateway->name] = [
                'total' => $total,
                'successful' => $successful,
                'failed' => $failed,
                'success_rate' => $successRate,
                'status' => $this->determineGatewayHealthStatus($successRate, $failed)
            ];
        }

        // Determine overall health status
        $overallSuccessRate = $health['last_24_hours']['success_rate'];
        $totalFailed = $health['last_24_hours']['failed'];

        if ($overallSuccessRate < 90 || $totalFailed > 10) {
            $health['overall_status'] = 'unhealthy';
            $health['alerts'][] = 'High webhook failure rate detected';
        } elseif ($overallSuccessRate < 95 || $totalFailed > 5) {
            $health['overall_status'] = 'warning';
            $health['alerts'][] = 'Elevated webhook failure rate';
        }

        return $health;
    }

    /**
     * Determine gateway health status
     */
    private function determineGatewayHealthStatus(float $successRate, int $failedCount): string
    {
        if ($successRate < 90 || $failedCount > 5) {
            return 'unhealthy';
        } elseif ($successRate < 95 || $failedCount > 2) {
            return 'warning';
        }

        return 'healthy';
    }
}