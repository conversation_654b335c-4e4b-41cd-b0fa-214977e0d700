@extends('layouts.app')

@section('content')
    <x-breadcrumb :segments="$breadcrumbs" :pageTitle="$pageTitle" :metaDescription="$metaDescription" />
    
    <div class="container mx-auto px-4 py-8 max-w-6xl bg-bg-light dark:bg-bg-dark min-h-screen">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 relative">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <article class="bg-white dark:bg-bg-dark rounded-xl shadow-lg border border-border-light dark:border-border-dark overflow-hidden">
                    @if ($post->featured_image)
                        <figure class="relative group">
                            <picture>
                                <source srcset="{{ uploads_url($post->featured_image) }}">
                                <img src="{{ uploads_url('assets/default-blog.webp') }}" alt="{{ $post->title }}"
                                    class="w-full h-72 object-cover group-hover:scale-105 transition-transform duration-500" loading="lazy">
                            </picture>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </figure>
                    @else
                        <div class="h-72 bg-gradient-to-br from-primary-light/20 to-accent-light/20 dark:from-primary-dark/20 dark:to-accent-dark/20 flex items-center justify-center">
                            <svg class="w-16 h-16 text-primary-light dark:text-primary-dark opacity-40" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    @endif

                    <div class="p-8">
                        <h1 class="text-4xl font-bold text-text-primary-light dark:text-text-primary-dark mb-6 leading-tight">
                            {{ $post->title }}
                        </h1>

                        <div class="flex items-center gap-4 mb-6 p-4 bg-bg-light dark:bg-bg-dark rounded-lg border border-border-light dark:border-border-dark">
                            <div class="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-primary-light to-accent-light dark:from-primary-dark dark:to-accent-dark flex items-center justify-center shadow-lg">
                                <span class="text-white font-bold text-lg">{{ substr($post->author->name, 0, 2) }}</span>
                            </div>
                            <div class="flex-1">
                                <div class="font-semibold text-text-primary-light dark:text-text-primary-dark">{{ $post->author->name }}</div>
                                <time datetime="{{ optional($post->published_at)->toDateString() }}" class="text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                    Published {{ optional($post->published_at)->diffForHumans() }}
                                    @if($post->updated_at > $post->created_at)
                                        <span class="mx-2">•</span>
                                        <span class="text-accent-light dark:text-accent-dark">Updated {{ $post->updated_at->diffForHumans() }}</span>
                                    @endif
                                </time>
                            </div>
                        </div>

                        @if ($post->tags && $post->tags->count() > 0)
                            <div class="flex flex-wrap gap-2 mb-6">
                                @foreach ($post->tags as $tag)
                                    <a href="{{ route('blog.tag', $tag->slug) }}"
                                        class="px-3 py-1 text-sm font-medium rounded-full border border-primary-light dark:border-primary-dark text-primary-light dark:text-primary-dark hover:bg-primary-light dark:hover:bg-primary-dark hover:text-white dark:hover:text-bg-dark transition-all duration-200 hover:scale-105">
                                        #{{ $tag->name }}
                                    </a>
                                @endforeach
                            </div>
                        @endif

                        @if ($post->category)
                            <div class="mb-6 p-3 bg-accent-light/10 dark:bg-accent-dark/10 rounded-lg border-l-4 border-accent-light dark:border-accent-dark">
                                <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium">Category:</span>
                                <a href="{{ route('blog.category', $post->category->slug) }}"
                                    class="text-accent-light dark:text-accent-dark hover:text-primary-light dark:hover:text-primary-dark ml-2 font-semibold transition-colors duration-200">
                                    {{ $post->category->name }}
                                </a>
                            </div>
                        @endif

                        <div class="prose dark:prose-invert lg:prose-xl max-w-none 
                                    prose-headings:font-bold prose-headings:text-text-primary-light dark:prose-headings:text-text-primary-dark
                                    prose-p:text-text-primary-light dark:prose-p:text-text-primary-dark prose-p:leading-relaxed
                                    prose-a:text-primary-light dark:prose-a:text-primary-dark prose-a:no-underline hover:prose-a:underline
                                    prose-img:rounded-xl prose-img:shadow-lg prose-img:border prose-img:border-border-light dark:prose-img:border-border-dark
                                    prose-blockquote:border-l-4 prose-blockquote:border-accent-light dark:prose-blockquote:border-accent-dark
                                    prose-blockquote:bg-accent-light/5 dark:prose-blockquote:bg-accent-dark/5
                                    prose-blockquote:pl-6 prose-blockquote:py-2 prose-blockquote:italic prose-blockquote:rounded-r-lg
                                    prose-strong:text-text-primary-light dark:prose-strong:text-text-primary-dark
                                    prose-code:text-accent-light dark:prose-code:text-accent-dark prose-code:bg-bg-light dark:prose-code:bg-bg-dark
                                    prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm
                                    prose-pre:bg-text-primary-light dark:prose-pre:bg-bg-dark prose-pre:text-text-primary-dark
                                    prose-pre:border prose-pre:border-border-light dark:prose-pre:border-border-dark
                                    prose-li:text-text-primary-light dark:prose-li:text-text-primary-dark
                                    prose-table:border prose-table:border-border-light dark:prose-table:border-border-dark
                                    prose-th:bg-bg-light dark:prose-th:bg-bg-dark prose-th:text-text-primary-light dark:prose-th:text-text-primary-dark
                                    prose-td:border-border-light dark:prose-td:border-border-dark" 
                             id="blog-content">
                            {!! $post->content !!}
                        </div>

                        <div class="border-t border-border-light dark:border-border-dark my-8"></div>

                        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <div class="flex items-center gap-4">
                                <span class="text-text-secondary-light dark:text-text-secondary-dark font-medium">Share this post:</span>
                                <div class="flex rounded-lg overflow-hidden shadow-sm">
                                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(url()->current()) }}&text={{ urlencode($post->title) }}"
                                        target="_blank" rel="noopener noreferrer"
                                        class="p-3 bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark hover:bg-blue-50 dark:hover:bg-blue-900/20 text-text-secondary-light dark:text-text-secondary-dark hover:text-blue-500 transition-all duration-200 group"
                                        title="Share on Twitter">
                                        <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                        </svg>
                                    </a>

                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"
                                        target="_blank" rel="noopener noreferrer"
                                        class="p-3 bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark border-l-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-text-secondary-light dark:text-text-secondary-dark hover:text-blue-600 transition-all duration-200 group"
                                        title="Share on Facebook">
                                        <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                        </svg>
                                    </a>

                                    <a href="https://www.linkedin.com/shareArticle?mini=true&url={{ urlencode(url()->current()) }}&title={{ urlencode($post->title) }}"
                                        target="_blank" rel="noopener noreferrer"
                                        class="p-3 bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark border-l-0 hover:bg-blue-50 dark:hover:bg-blue-900/20 text-text-secondary-light dark:text-text-secondary-dark hover:text-blue-700 transition-all duration-200 group"
                                        title="Share on LinkedIn">
                                        <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                        </svg>
                                    </a>

                                    <button onclick="copyToClipboard('{{ url()->current() }}')"
                                        class="p-3 bg-white dark:bg-bg-dark border border-border-light dark:border-border-dark border-l-0 hover:bg-green-50 dark:hover:bg-green-900/20 text-text-secondary-light dark:text-text-secondary-dark hover:text-green-600 transition-all duration-200 group"
                                        title="Copy Link">
                                        <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center gap-2 text-text-secondary-light dark:text-text-secondary-dark text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <span>{{ $post->views ?? 0 }} views</span>
                            </div>
                        </div>
                    </div>
                </article>

                @if ($relatedPosts->count() > 0)
                    <div class="mt-12">
                        <h2 class="text-2xl font-bold mb-6 text-text-primary-light dark:text-text-primary-dark flex items-center gap-2">
                            <svg class="w-6 h-6 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Related Posts
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach ($relatedPosts as $relatedPost)
                                <article class="group bg-white dark:bg-bg-dark rounded-xl shadow-lg border border-border-light dark:border-border-dark overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02]">
                                    @if ($relatedPost->featured_image)
                                        <figure class="relative overflow-hidden">
                                            <picture>
                                                <source srcset="{{ uploads_url($relatedPost->featured_image) }}">
                                                <img src="{{ uploads_url('assets/default-blog.webp') }}"
                                                    alt="{{ $relatedPost->title }}" class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                                                    loading="lazy">
                                            </picture>
                                        </figure>
                                    @else
                                        <div class="h-48 bg-gradient-to-br from-primary-light/10 to-accent-light/10 dark:from-primary-dark/10 dark:to-accent-dark/10 flex items-center justify-center">
                                            <svg class="w-12 h-12 text-primary-light dark:text-primary-dark opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                    @endif
                                    <div class="p-6">
                                        <h3 class="font-bold text-lg mb-2 leading-tight">
                                            <a href="{{ route('blog.show', $relatedPost->slug) }}"
                                                class="text-text-primary-light dark:text-text-primary-dark hover:text-primary-light dark:hover:text-primary-dark transition-colors duration-200">
                                                {{ $relatedPost->title }}
                                            </a>
                                        </h3>
                                        <p class="text-text-secondary-light dark:text-text-secondary-dark text-sm mb-4 line-clamp-3">
                                            {{ $relatedPost->excerpt ?? Str::limit(strip_tags($relatedPost->content), 120) }}
                                        </p>
                                        <div class="flex items-center justify-between text-sm text-text-secondary-light dark:text-text-secondary-dark">
                                            <time datetime="{{ optional($relatedPost->published_at)->toDateString() }}">
                                                {{ optional($relatedPost->published_at)->format('M d, Y') }}
                                            </time>
                                            <span class="text-accent-light dark:text-accent-dark font-medium">Read more →</span>
                                        </div>
                                    </div>
                                </article>
                            @endforeach
                        </div>
                    </div>
                @endif

                <div class="mt-12">
                    <div class="bg-white dark:bg-bg-dark rounded-xl shadow-lg border border-border-light dark:border-border-dark overflow-hidden">
                        <div class="p-8">
                            <h2 class="text-2xl font-bold text-text-primary-light dark:text-text-primary-dark mb-6 flex items-center gap-2">
                                <svg class="w-6 h-6 text-accent-light dark:text-accent-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                Comments ({{ $post->comments->whereNull('parent_id')->count() }})
                            </h2>

                            @auth
                                <form action="{{ route('comments.store', $post) }}" method="POST" class="space-y-4 mb-8">
                                    @csrf
                                    <h3 class="text-lg font-semibold mb-2">Write a comment</h3>
                                    <div class="relative">
                                        <textarea name="content"
                                            class="w-full px-4 py-3 border border-border-light dark:border-border-dark rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-primary-dark focus:border-transparent bg-white dark:bg-bg-dark text-text-primary-light dark:text-text-primary-dark transition-all duration-200 resize-none"
                                            placeholder="Share your thoughts..." rows="4" required></textarea>
                                        <div class="absolute bottom-3 right-3 text-text-secondary-light dark:text-text-secondary-dark text-sm">
                                            Press Ctrl+Enter to submit
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-2 text-text-secondary-light dark:text-text-secondary-dark text-sm">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Be respectful and constructive
                                        </div>
                                        <button type="submit"
                                            class="px-6 py-3 bg-primary-light hover:bg-primary-dark dark:bg-primary-dark dark:hover:bg-primary-light text-white rounded-lg transition-all duration-200 hover:scale-105 font-medium">
                                            Submit Comment
                                        </button>
                                    </div>
                                </form>
                            @else
                                <div class="flex items-center p-4 bg-accent-light/10 dark:bg-accent-dark/10 text-accent-light dark:text-accent-dark rounded-lg mb-8 border border-accent-light/20 dark:border-accent-dark/20">
                                    <svg class="w-6 h-6 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span><strong>Login to comment</strong>. <a href="{{ route('login') }}" class="font-semibold hover:underline">Login</a></span>
                                </div>
                            @endauth

                            @if($post->comments->whereNull('parent_id')->count() > 0)
                                <div class="border-t border-border-light dark:border-border-dark my-8"></div>
                                <div class="space-y-6">
                                    @foreach ($post->comments->whereNull('parent_id') as $comment)
                                        <x-comment :comment="$comment" />
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-8 text-text-secondary-light dark:text-text-secondary-dark">
                                    <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    <p>No comments yet. Be the first to share your thoughts!</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="lg:sticky lg:top-20">
                    <div class="bg-white dark:bg-bg-dark rounded-xl border border-border-light dark:border-border-dark shadow-lg">
                        @include('pincodes.partials.sidebar')
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Custom styles for line clamping */
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Enhanced prose styling */
        .prose img {
            margin: 2rem auto;
            max-width: 100%;
            height: auto;
        }

        .prose table {
            width: 100%;
            margin: 2rem 0;
        }

        .prose table th,
        .prose table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid theme('colors.border.light');
        }

        .dark .prose table th,
        .dark .prose table td {
            border-bottom-color: theme('colors.border.dark');
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            @apply bg-bg-light dark:bg-bg-dark;
        }
        
        ::-webkit-scrollbar-thumb {
            @apply bg-border-light dark:bg-border-dark rounded-full;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            @apply bg-primary-light dark:bg-primary-dark;
        }

        /* Smooth transitions */
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* Toast notification styles */
        .toast {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to toggle edit form visibility
            window.toggleEditForm = function(commentId) {
                const editForm = document.getElementById(`edit-form-${commentId}`);
                if (editForm) {
                    editForm.classList.toggle('hidden');
                }
            };

            // Function to toggle reply form visibility
            window.toggleReplyForm = function(commentId) {
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                if (replyForm) {
                    replyForm.classList.toggle('hidden');
                }
            };

            // Function to copy URL to clipboard
            window.copyToClipboard = function(text) {
                navigator.clipboard.writeText(text).then(() => {
                    // Create and display toast notification
                    const toast = document.createElement('div');
                    toast.className =
                        'fixed top-4 right-4 px-4 py-2 bg-green-500 text-white rounded-lg shadow-lg z-50 transform transition-all duration-300';
                    toast.textContent = 'Link copied';
                    document.body.appendChild(toast);

                    // Remove toast after 3 seconds
                    setTimeout(() => {
                        toast.classList.add('opacity-0');
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 300);
                    }, 3000);
                }).catch(err => {
                    console.error('Failed to copy', err);
                });
            };

            // Find and attach event listeners to all comment forms
            // Main comment form
            const mainCommentForm = document.querySelector('form[action*="comments.store"]');
            if (mainCommentForm) {
                mainCommentForm.addEventListener('submit', function(e) {
                    handleFormSubmit(e, this, () => {
                        showToast("Comment added", "success");
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    });
                });
            }

            // Edit comment forms
            document.querySelectorAll('form[id^="edit-form-"]').forEach(form => {
                form.addEventListener('submit', function(e) {
                    handleFormSubmit(e, this, (result) => {
                        const commentId = this.getAttribute('data-comment-id');
                        const commentContent = document.getElementById(
                            `comment-content-${commentId}`);
                        if (commentContent) {
                            commentContent.textContent = result.content;
                        }
                        toggleEditForm(commentId);
                        showToast("Comment updated", "success");
                    });
                });
            });

            // Reply comment forms
            document.querySelectorAll('form[id^="reply-form-"]').forEach(form => {
                form.addEventListener('submit', function(e) {
                    handleFormSubmit(e, this, () => {
                        showToast("Reply added", "success");
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    });
                });
            });

            function handleFormSubmit(e, form, successCallback) {
                e.preventDefault();
                const formData = new FormData(form);
                const url = form.getAttribute('action');
                const method = form.getAttribute('method') || 'POST';

                fetch(url, {
                        method: method,
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network error');
                        }
                        return response.json();
                    })
                    .then(result => {
                        form.reset();
                        if (successCallback) successCallback(result);
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showToast("Error submitting comment", "error");
                    });
            }

            // Helper function to show toast notification
            function showToast(message, type) {
                const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
                const toast = document.createElement('div');
                toast.className =
                    `fixed top-4 right-4 px-4 py-2 ${bgColor} text-white rounded-lg shadow-lg z-50 transform transition-all duration-300`;
                toast.textContent = message;
                document.body.appendChild(toast);

                // Remove toast after 3 seconds
                setTimeout(() => {
                    toast.classList.add('opacity-0');
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 3000);
            }
        });
    </script>
@endpush

