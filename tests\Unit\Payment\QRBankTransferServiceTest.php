<?php

namespace Tests\Unit\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\PaymentProof;
use App\Services\Payment\QRBankTransferService;
use App\Services\Payment\Responses\PaymentResponse;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class QRBankTransferServiceTest extends TestCase
{
    use RefreshDatabase;

    protected QRBankTransferService $service;
    protected PaymentGateway $gateway;
    protected Order $order;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test gateway configuration
        $this->gateway = PaymentGateway::factory()->create([
            'name' => 'qr_bank_transfer',
            'display_name' => 'Bank Transfer (QR)',
            'is_active' => true,
            'configuration' => [
                'bank_name' => 'Test Bank',
                'account_name' => 'Test Company Ltd',
                'account_number' => '**********',
                'ifsc_code' => 'TEST0001234',
                'branch_name' => 'Test Branch',
                'upi_id' => 'testcompany@testbank'
            ],
            'supported_currencies' => ['INR', 'USD']
        ]);

        $this->order = Order::factory()->create([
            'amount' => 1000.00,
            'currency' => 'INR',
            'order_number' => 'ORD-TEST-001'
        ]);

        $this->service = new QRBankTransferService($this->gateway);

        // Setup storage for testing
        Storage::fake('private');
    }

    public function test_creates_payment_successfully()
    {
        $response = $this->service->createPayment($this->order);

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertNotNull($response->paymentId);
        $this->assertEquals('pending', $response->status);
        $this->assertArrayHasKey('qr_data', $response->metadata);
        $this->assertArrayHasKey('bank_details', $response->metadata);
        $this->assertArrayHasKey('payment_reference', $response->metadata);
    }

    public function test_validates_required_configuration()
    {
        // Test with missing configuration
        $invalidGateway = PaymentGateway::factory()->create([
            'name' => 'qr_bank_transfer',
            'configuration' => []
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Missing required bank transfer configuration');

        new QRBankTransferService($invalidGateway);
    }

    public function test_validates_currency_support()
    {
        $unsupportedOrder = Order::factory()->create([
            'amount' => 100.00,
            'currency' => 'EUR'
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Currency EUR not supported by QR Bank Transfer gateway');

        $this->service->createPayment($unsupportedOrder);
    }

    public function test_generates_qr_code_data()
    {
        $qrData = $this->service->generateQRCodeData($this->order);

        $this->assertIsString($qrData);
        $this->assertStringContainsString('upi://pay', $qrData);
        $this->assertStringContainsString('pa=testcompany@testbank', $qrData);
        $this->assertStringContainsString('am=1000.00', $qrData);
        $this->assertStringContainsString('tn=Payment for ORD-TEST-001', $qrData);
    }

    public function test_generates_bank_details()
    {
        $bankDetails = $this->service->getBankDetails();

        $this->assertIsArray($bankDetails);
        $this->assertEquals('Test Company Ltd', $bankDetails['account_name']);
        $this->assertEquals('**********', $bankDetails['account_number']);
        $this->assertEquals('TEST0001234', $bankDetails['ifsc_code']);
        $this->assertEquals('Test Bank', $bankDetails['bank_name']);
        $this->assertEquals('Test Branch', $bankDetails['branch_name']);
    }

    public function test_generates_payment_reference()
    {
        $reference = $this->service->generatePaymentReference($this->order);

        $this->assertIsString($reference);
        $this->assertStringContainsString('QR', $reference);
        $this->assertStringContainsString($this->order->order_number, $reference);
        $this->assertEquals(20, strlen($reference)); // Expected length
    }

    public function test_creates_payment_record()
    {
        $response = $this->service->createPayment($this->order);

        // Verify payment record was created
        $payment = Payment::where('order_id', $this->order->id)->first();
        
        $this->assertNotNull($payment);
        $this->assertEquals($this->gateway->id, $payment->gateway_id);
        $this->assertEquals('qr_bank_transfer', $payment->payment_method);
        $this->assertEquals(Payment::STATUS_PENDING, $payment->payment_status);
        $this->assertEquals($this->order->amount, $payment->amount);
        $this->assertEquals($this->order->currency, $payment->currency);
    }

    public function test_verifies_payment_with_proof()
    {
        // Create a payment first
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'gateway_id' => $this->gateway->id,
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        // Create payment proof
        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $payment->id,
            'verification_status' => PaymentProof::STATUS_APPROVED
        ]);

        $response = $this->service->verifyPayment($payment->gateway_payment_id);

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals('completed', $response->status);
    }

    public function test_rejects_payment_without_proof()
    {
        // Create a payment without proof
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'gateway_id' => $this->gateway->id,
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $response = $this->service->verifyPayment($payment->gateway_payment_id);

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertFalse($response->success);
        $this->assertEquals('pending', $response->status);
        $this->assertStringContainsString('No payment proof uploaded', $response->message);
    }

    public function test_handles_rejected_payment_proof()
    {
        // Create a payment with rejected proof
        $payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'gateway_id' => $this->gateway->id,
            'payment_method' => 'qr_bank_transfer',
            'payment_status' => Payment::STATUS_PENDING
        ]);

        $paymentProof = PaymentProof::factory()->create([
            'payment_id' => $payment->id,
            'verification_status' => PaymentProof::STATUS_REJECTED,
            'admin_notes' => 'Invalid payment proof'
        ]);

        $response = $this->service->verifyPayment($payment->gateway_payment_id);

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertFalse($response->success);
        $this->assertEquals('failed', $response->status);
        $this->assertStringContainsString('Payment proof rejected', $response->message);
    }

    public function test_formats_amount_for_display()
    {
        $amount = 1234.56;
        $currency = 'INR';

        $formatted = $this->service->formatAmountForDisplay($amount, $currency);

        $this->assertEquals('₹1,234.56', $formatted);
    }

    public function test_validates_minimum_amount()
    {
        $smallOrder = Order::factory()->create([
            'amount' => 0.50, // Below minimum
            'currency' => 'INR'
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Amount below minimum allowed for bank transfer');

        $this->service->createPayment($smallOrder);
    }

    public function test_validates_maximum_amount()
    {
        $largeOrder = Order::factory()->create([
            'amount' => 1000000.00, // Above maximum
            'currency' => 'INR'
        ]);

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Amount exceeds maximum allowed for bank transfer');

        $this->service->createPayment($largeOrder);
    }

    public function test_generates_unique_payment_references()
    {
        $reference1 = $this->service->generatePaymentReference($this->order);
        $reference2 = $this->service->generatePaymentReference($this->order);

        $this->assertNotEquals($reference1, $reference2);
    }

    public function test_handles_webhook_not_supported()
    {
        $request = Request::create('/webhook', 'POST', [], [], [], [], '{}');

        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Webhooks not supported for QR Bank Transfer');

        $this->service->handleWebhook($request);
    }

    public function test_handles_refund_not_supported()
    {
        $this->expectException(PaymentGatewayException::class);
        $this->expectExceptionMessage('Refunds not supported for QR Bank Transfer');

        $this->service->refundPayment('payment_123', 100.00);
    }

    public function test_gets_payment_status()
    {
        $payment = Payment::factory()->create([
            'gateway_payment_id' => 'qr_payment_123',
            'payment_status' => Payment::STATUS_COMPLETED
        ]);

        $response = $this->service->getPaymentStatus('qr_payment_123');

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals('completed', $response->status);
    }

    public function test_handles_payment_not_found()
    {
        $response = $this->service->getPaymentStatus('nonexistent_payment');

        $this->assertInstanceOf(PaymentResponse::class, $response);
        $this->assertFalse($response->success);
        $this->assertStringContainsString('Payment not found', $response->message);
    }

    public function test_generates_instructions_for_customer()
    {
        $instructions = $this->service->getPaymentInstructions($this->order);

        $this->assertIsArray($instructions);
        $this->assertArrayHasKey('steps', $instructions);
        $this->assertArrayHasKey('important_notes', $instructions);
        $this->assertArrayHasKey('support_info', $instructions);
        
        $this->assertIsArray($instructions['steps']);
        $this->assertGreaterThan(0, count($instructions['steps']));
    }

    public function test_validates_bank_configuration_completeness()
    {
        $incompleteConfig = [
            'account_name' => 'Test Company',
            // Missing other required fields
        ];

        $incompleteGateway = PaymentGateway::factory()->create([
            'name' => 'qr_bank_transfer',
            'configuration' => $incompleteConfig
        ]);

        $this->expectException(PaymentGatewayException::class);

        new QRBankTransferService($incompleteGateway);
    }

    public function test_supports_multiple_currencies()
    {
        $usdOrder = Order::factory()->create([
            'amount' => 100.00,
            'currency' => 'USD'
        ]);

        $response = $this->service->createPayment($usdOrder);

        $this->assertTrue($response->success);
        $this->assertEquals('USD', $response->currency);
    }

    public function test_logs_payment_creation()
    {
        // This would test that appropriate logs are created
        // For now, we'll just verify the method completes successfully
        $response = $this->service->createPayment($this->order);

        $this->assertTrue($response->success);
        
        // In a real implementation, you'd check log files or use a log testing framework
        // $this->assertLogContains('QR Bank Transfer payment created');
    }
}