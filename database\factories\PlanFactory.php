<?php

namespace Database\Factories;

use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition()
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->sentence(),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'is_active' => true,
            'features' => [
                'feature1' => $this->faker->sentence(),
                'feature2' => $this->faker->sentence(),
            ],
            'slug' => $this->faker->unique()->slug(),
            'request_limit' => $this->faker->numberBetween(1, 1000),
        ];
    }
} 