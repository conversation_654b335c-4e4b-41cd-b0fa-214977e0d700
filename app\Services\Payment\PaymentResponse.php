<?php

namespace App\Services\Payment;

class PaymentResponse
{
    public bool $success;
    public ?string $paymentId = null;
    public ?string $orderId = null;
    public ?string $checkoutUrl = null;
    public ?string $status = null;
    public ?string $message = null;
    public ?string $errorCode = null;
    public ?array $gatewayResponse = null;
    public ?array $metadata = null;

    public function __construct(bool $success = false)
    {
        $this->success = $success;
    }

    /**
     * Create a successful payment response.
     */
    public static function success(array $data = []): self
    {
        $response = new self(true);
        
        if (isset($data['payment_id'])) {
            $response->paymentId = $data['payment_id'];
        }
        
        if (isset($data['order_id'])) {
            $response->orderId = $data['order_id'];
        }
        
        if (isset($data['checkout_url'])) {
            $response->checkoutUrl = $data['checkout_url'];
        }
        
        if (isset($data['status'])) {
            $response->status = $data['status'];
        }
        
        if (isset($data['message'])) {
            $response->message = $data['message'];
        }
        
        if (isset($data['gateway_response'])) {
            $response->gatewayResponse = $data['gateway_response'];
        }
        
        if (isset($data['metadata'])) {
            $response->metadata = $data['metadata'];
        }
        
        return $response;
    }

    /**
     * Create an error payment response.
     */
    public static function error(string $message, ?string $errorCode = null, ?array $gatewayResponse = null): self
    {
        $response = new self(false);
        $response->message = $message;
        $response->errorCode = $errorCode;
        $response->gatewayResponse = $gatewayResponse;
        
        return $response;
    }

    /**
     * Set payment ID.
     */
    public function setPaymentId(string $paymentId): self
    {
        $this->paymentId = $paymentId;
        return $this;
    }

    /**
     * Set order ID.
     */
    public function setOrderId(string $orderId): self
    {
        $this->orderId = $orderId;
        return $this;
    }

    /**
     * Set checkout URL.
     */
    public function setCheckoutUrl(string $checkoutUrl): self
    {
        $this->checkoutUrl = $checkoutUrl;
        return $this;
    }

    /**
     * Set payment status.
     */
    public function setStatus(string $status): self
    {
        $this->status = $status;
        return $this;
    }

    /**
     * Set message.
     */
    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    /**
     * Set gateway response data.
     */
    public function setGatewayResponse(array $gatewayResponse): self
    {
        $this->gatewayResponse = $gatewayResponse;
        return $this;
    }

    /**
     * Set metadata.
     */
    public function setMetadata(array $metadata): self
    {
        $this->metadata = $metadata;
        return $this;
    }

    /**
     * Check if the response is successful.
     */
    public function isSuccess(): bool
    {
        return $this->success;
    }

    /**
     * Check if the response is an error.
     */
    public function isError(): bool
    {
        return !$this->success;
    }

    /**
     * Get the response as an array.
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'payment_id' => $this->paymentId,
            'order_id' => $this->orderId,
            'checkout_url' => $this->checkoutUrl,
            'status' => $this->status,
            'message' => $this->message,
            'error_code' => $this->errorCode,
            'gateway_response' => $this->gatewayResponse,
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Convert to JSON.
     */
    public function toJson(): string
    {
        return json_encode($this->toArray());
    }
}