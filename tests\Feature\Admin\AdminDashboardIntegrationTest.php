<?php

use App\Models\User;
use App\Models\PinCode;
use App\Models\Order;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

beforeEach(function () {
    $this->admin = User::factory()->create([
        'role' => 'admin',
        'status' => 'active'
    ]);
    
    $this->actingAs($this->admin);
});

describe('Admin Dashboard Integration', function () {
    
    describe('Dashboard Statistics', function () {
        it('displays correct user statistics', function () {
            User::factory()->count(5)->create(['status' => 'active']);
            User::factory()->count(2)->create(['status' => 'inactive']);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.dashboard');
            $response->assertViewHas('stats');
            
            $stats = $response->viewData('stats');
            expect($stats['total_users'])->toBe(8); // 5 active + 2 inactive + 1 admin
            expect($stats['active_users'])->toBe(6); // 5 active + 1 admin
        });

        it('displays correct pincode statistics', function () {
            PinCode::factory()->count(100)->create();

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
            $stats = $response->viewData('stats');
            expect($stats['total_pincodes'])->toBe(100);
        });

        it('caches dashboard statistics', function () {
            Cache::shouldReceive('remember')
                ->once()
                ->with('admin.dashboard.stats', 3600, \Closure::class)
                ->andReturn([
                    'total_users' => 10,
                    'active_users' => 8,
                    'total_pincodes' => 50,
                    'system_health' => []
                ]);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
        });

        it('includes system health information', function () {
            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(200);
            $stats = $response->viewData('stats');
            
            expect($stats['system_health'])->toHaveKeys([
                'php_version',
                'laravel_version',
                'disk_usage'
            ]);
        });
    });

    describe('Dashboard API Stats', function () {
        it('returns stats as JSON', function () {
            User::factory()->count(3)->create();
            Order::factory()->count(2)->create(['amount' => 100]);

            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertStatus(200);
            $response->assertJsonStructure([
                'stats' => [
                    'users',
                    'revenue',
                    'subscriptions',
                    'pageViews',
                    'system' => [
                        'php_version',
                        'laravel_version',
                        'memory',
                        'disk'
                    ]
                ],
                'activities'
            ]);
        });

        it('calculates revenue correctly', function () {
            Order::factory()->create(['amount' => 150]);
            Order::factory()->create(['amount' => 250]);

            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertStatus(200);
            $data = $response->json();
            expect($data['stats']['revenue'])->toBe(400);
        });

        it('includes system memory information', function () {
            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertStatus(200);
            $data = $response->json();
            
            expect($data['stats']['system']['memory'])->toHaveKeys([
                'used', 'total', 'percentage'
            ]);
        });

        it('includes disk usage information', function () {
            $response = $this->get(route('admin.dashboard.stats'));

            $response->assertStatus(200);
            $data = $response->json();
            
            expect($data['stats']['system']['disk'])->toHaveKeys([
                'used', 'total', 'percentage'
            ]);
        });
    });

    describe('System Operations', function () {
        it('clears cache successfully', function () {
            Cache::put('test_key', 'test_value');
            
            $response = $this->get(route('admin.clear-cache'));

            $response->assertRedirect();
            $response->assertSessionHas('success', 'Cache cleared successfully');
        });

        it('toggles maintenance mode', function () {
            $response = $this->post(route('admin.maintenance.toggle'));

            $response->assertRedirect();
            $response->assertSessionHas('success');
        });
    });

    describe('Profile Management', function () {
        it('displays admin profile page', function () {
            $response = $this->get(route('admin.profile.index'));

            $response->assertStatus(200);
            $response->assertViewIs('admin.profile');
            $response->assertViewHas('user', $this->admin);
        });

        it('updates admin profile successfully', function () {
            $updateData = [
                'name' => 'Updated Admin Name',
                'email' => '<EMAIL>'
            ];

            $response = $this->put(route('admin.profile.update'), $updateData);

            $response->assertRedirect();
            $response->assertSessionHas('success', 'Profile updated successfully!');
            
            $this->admin->refresh();
            expect($this->admin->name)->toBe('Updated Admin Name');
            expect($this->admin->email)->toBe('<EMAIL>');
        });

        it('validates profile update data', function () {
            $response = $this->put(route('admin.profile.update'), [
                'name' => '',
                'email' => 'invalid-email'
            ]);

            $response->assertSessionHasErrors(['name', 'email']);
        });

        it('updates admin password successfully', function () {
            $response = $this->put(route('admin.profile.password'), [
                'current_password' => 'password',
                'new_password' => 'newpassword123',
                'new_password_confirmation' => 'newpassword123'
            ]);

            $response->assertRedirect();
            $response->assertSessionHas('success', 'Password updated successfully!');
        });

        it('validates current password when updating', function () {
            $response = $this->put(route('admin.profile.password'), [
                'current_password' => 'wrongpassword',
                'new_password' => 'newpassword123',
                'new_password_confirmation' => 'newpassword123'
            ]);

            $response->assertSessionHasErrors(['current_password']);
        });

        it('validates password confirmation', function () {
            $response = $this->put(route('admin.profile.password'), [
                'current_password' => 'password',
                'new_password' => 'newpassword123',
                'new_password_confirmation' => 'differentpassword'
            ]);

            $response->assertSessionHasErrors(['new_password']);
        });
    });

    describe('Two-Factor Authentication', function () {
        it('enables two-factor authentication', function () {
            $response = $this->post(route('admin.profile.2fa.enable'));

            $response->assertRedirect(route('admin.profile.index'));
            $response->assertSessionHas('success', 'Two-factor authentication enabled successfully.');
            
            $this->admin->refresh();
            expect($this->admin->two_factor_enabled)->toBe(true);
        });

        it('disables two-factor authentication', function () {
            $this->admin->update(['two_factor_enabled' => true]);

            $response = $this->post(route('admin.profile.2fa.disable'));

            $response->assertRedirect(route('admin.profile.index'));
            $response->assertSessionHas('success', 'Two-factor authentication disabled successfully.');
            
            $this->admin->refresh();
            expect($this->admin->two_factor_enabled)->toBe(false);
        });
    });

    describe('Authorization', function () {
        it('prevents non-admin users from accessing dashboard', function () {
            $regularUser = User::factory()->create(['role' => 'user']);
            $this->actingAs($regularUser);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(403);
        });

        it('prevents inactive admin from accessing dashboard', function () {
            $inactiveAdmin = User::factory()->create([
                'role' => 'admin',
                'status' => 'inactive'
            ]);
            $this->actingAs($inactiveAdmin);

            $response = $this->get(route('admin.dashboard'));

            $response->assertStatus(403);
        });
    });
});