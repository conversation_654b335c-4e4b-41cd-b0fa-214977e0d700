<?php

use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\ApiTokenController;
use App\Http\Controllers\Admin\BlogCategoryController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\BlogTagController;
use App\Http\Controllers\Admin\CacheController;
use App\Http\Controllers\Admin\CommentController;
use App\Http\Controllers\Admin\CourierDictController;
use App\Http\Controllers\Admin\DistrictController;
use App\Http\Controllers\Admin\LandingPageController;
use App\Http\Controllers\Admin\MailConfigController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PaymentController;
use App\Http\Controllers\Admin\PincodeController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\ReviewController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\StateController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\TawkSettingsController;
use App\Http\Controllers\Admin\TypographyController;
use App\Http\Controllers\Admin\ThemeController;
use App\Http\Controllers\Admin\TestimonialController;

Route::prefix('admin')->name('admin.')->group(function () {
    // Admin Authentication Routes
    Route::middleware('guest')->group(function () {
        Route::get('/login', [AdminAuthController::class, 'showLoginForm'])->name('login');
        Route::post('/login', [AdminAuthController::class, 'login'])->name('login.post');
    });

    Route::post('/logout', [AdminAuthController::class, 'logout'])
        ->name('logout')
        ->middleware('auth');

    Route::middleware(['auth', 'admin'])->group(function () {
        // Dashboard
        Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard/stats', [AdminController::class, 'getStats'])->name('dashboard.stats');

        // contact number changes
        Route::prefix('contact-number-changes')->group(function () {
            Route::get('/stats', [App\Http\Controllers\Admin\ContactNumberChangeController::class, 'getStats'])
                ->name('contact-number-changes.stats');
            Route::get('/', [App\Http\Controllers\Admin\ContactNumberChangeController::class, 'index'])
                ->name('contact-number-changes.index');
            Route::get('/{change}', [App\Http\Controllers\Admin\ContactNumberChangeController::class, 'show'])
                ->name('contact-number-changes.show');
            Route::post('/{change}', [App\Http\Controllers\Admin\ContactNumberChangeController::class, 'update'])
                ->name('contact-number-changes.update');
        });

        // Profile Management
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [AdminController::class, 'profile'])->name('index');
            Route::put('/', [AdminController::class, 'updateProfile'])->name('update');
            Route::put('/password', [AdminController::class, 'updatePassword'])->name('password');
            Route::post('/2fa/enable', [AdminController::class, 'enableTwoFactor'])->name('2fa.enable');
            Route::post('/2fa/disable', [AdminController::class, 'disableTwoFactor'])->name('2fa.disable');
        });

        // System Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingController::class, 'index'])->name('index');
            Route::put('/', [SettingController::class, 'update'])->name('update');
            Route::post('/maintenance', [SettingController::class, 'maintenanceMode'])->name('maintenance');
            Route::post('/login', [SettingController::class, 'toggleLogin'])->name('login');

            Route::post('/api', [AdminController::class, 'updateApiSettings'])->name('api.update');
            // Route::post('/email', [AdminController::class, 'updateEmailSettings'])->name('email.update');
        });

        // Mail Configuration
        Route::prefix('mail-config')->name('mail-config.')->group(function () {
            Route::get('/', [MailConfigController::class, 'index'])->name('index');

            Route::post('/update', [MailConfigController::class, 'update'])->name('update');
            Route::post('/test-mail', [MailConfigController::class, 'sendTestEmail'])->name('test');
        });

        // System Maintenance
        Route::post('/maintenance/toggle', [AdminController::class, 'toggleMaintenance'])->name('maintenance.toggle');
        // Route::post('/cache/clear', [AdminController::class, 'clearCache'])->name('cache.clear');
        Route::get('/clear-cache', [CacheController::class, 'clearCache'])->name('clear-cache');

        // User Management
        Route::resource('users', UserController::class);

        // Content Management
        Route::resource('pages', PageController::class);

        // Location Management
        Route::resource('states', StateController::class);
        Route::resource('districts', DistrictController::class);

        // Pincode Import Routes (must come before resource route to avoid conflicts)
        Route::prefix('pincodes')->name('pincodes.')->group(function () {
            Route::get('/import', [PincodeController::class, 'showImportForm'])->name('import');
            Route::post('/import/process', [PincodeController::class, 'processImport'])->name('import.process');
            Route::get('/import/template', [PincodeController::class, 'downloadTemplate'])->name('download.template');
            Route::get('/import/{import}/errors', [PincodeController::class, 'showImportErrors'])->name('import.errors');
            Route::get('/import/active', [PincodeController::class, 'getActiveImports'])->name('import.active');
        });

        Route::resource('pincodes', PincodeController::class);

        Route::post('/backup/database', [AdminController::class, 'backupDatabase'])->name('backup.database');
        Route::post('/generate-api-key', [SettingController::class, 'generateApiKey'])->name('generate.apikey');

        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', function () {
                return redirect()->route('admin.blog.posts.index');
            })->name('index');

            Route::resource('posts', BlogController::class)->parameters([
                'posts' => 'post'
            ])->except(['show']);

            // Blog Categories
            Route::resource('categories', BlogCategoryController::class)->except(['show']);
            Route::get('categories/api', [BlogCategoryController::class, 'apiIndex'])->name('categories.api');

            // Blog Tags
            Route::resource('tags', BlogTagController::class)->except(['show']);
            Route::get('tags/api', [BlogTagController::class, 'apiIndex'])->name('tags.api');

            // Blog Image Upload
            Route::post('upload-image', [BlogController::class, 'uploadImage'])->name('upload-image');
        });

        // Comments Management
        Route::resource('comments', CommentController::class)->except(['create', 'store', 'edit', 'update']);
        Route::patch('comments/{comment}/approve', [CommentController::class, 'approve'])->name('comments.approve');
        Route::patch('comments/{comment}/reject', [CommentController::class, 'reject'])->name('comments.reject');

        // Landing Page Management
        Route::prefix('landing-page')->name('landing-page.')->group(function () {
            Route::get('/', [LandingPageController::class, 'index'])->name('index');
            Route::get('/{section}/edit', [LandingPageController::class, 'edit'])->name('edit');
            Route::put('/{section}', [LandingPageController::class, 'update'])->name('update');
            Route::patch('/{section}/toggle', [LandingPageController::class, 'toggleActive'])->name('toggle-active');
            Route::post('/reorder', [LandingPageController::class, 'reorder'])->name('reorder');
        });

        // Theme Management
        // Route::prefix('theme')->name('theme.')->group(function () {
        //     Route::get('/', [ThemeController::class, 'index'])->name('index');
        //     Route::post('/colors', [ThemeController::class, 'updateColors'])->name('colors.update');
        //     Route::post('/typography', [ThemeController::class, 'updateTypography'])->name('typography.update');
        //     Route::post('/layout', [ThemeController::class, 'updateLayout'])->name('layout.update');
        //     Route::post('/components', [ThemeController::class, 'updateComponents'])->name('components.update');
        //     Route::post('/seo', [ThemeController::class, 'updateSeo'])->name('seo.update');
        //     Route::post('/social', [ThemeController::class, 'updateSocial'])->name('social.update');
        //     Route::post('/analytics', [ThemeController::class, 'updateAnalytics'])->name('analytics.update');
        //     Route::post('/features', [ThemeController::class, 'toggleFeatures'])->name('features.update');
        //     Route::post('/variant', [ThemeController::class, 'setTheme'])->name('variant.update');
        //     Route::post('/reset', [ThemeController::class, 'resetTheme'])->name('reset');
        //     Route::get('/export', [ThemeController::class, 'exportTheme'])->name('export');
        //     Route::post('/import', [ThemeController::class, 'importTheme'])->name('import');
        //     Route::post('/palette', [ThemeController::class, 'updatePalette'])->name('palette.update');
        //     Route::post('/refresh-css', [ThemeController::class, 'refreshCss'])->name('refresh-css');
        //     Route::post('/generate-palette', [ThemeController::class, 'generatePalette'])->name('generate-palette');
        //     Route::post('/apply-palette', [ThemeController::class, 'applyPalette'])->name('apply-palette');
        // });

        Route::resource('courier-dict', CourierDictController::class);
        Route::get('courier-dict-search', [CourierDictController::class, 'search'])->name('courier-dict.search');
        Route::resource('orders', OrderController::class);

        // Review Management Routes
        Route::get('/reviews', [ReviewController::class, 'index'])->name('admin.reviews.index');
        Route::get('/reviews/{review}/edit', [ReviewController::class, 'edit'])->name('admin.reviews.edit');
        Route::put('/reviews/{review}', [ReviewController::class, 'update'])->name('admin.reviews.update');
        Route::delete('/reviews/{review}', [ReviewController::class, 'destroy'])->name('admin.reviews.destroy');
        Route::patch('/reviews/{review}/approve', [ReviewController::class, 'approve'])->name('admin.reviews.approve');
        Route::patch('/reviews/{review}/reject', [ReviewController::class, 'reject'])->name('admin.reviews.reject');

        // Plan Management Routes
        Route::get('/plans', [PlanController::class, 'index'])->name('plans.index');
        Route::get('/plans/create', [PlanController::class, 'create'])->name('plans.create');
        Route::post('/plans', [PlanController::class, 'store'])->name('plans.store');
        Route::get('/plans/{plan}/edit', [PlanController::class, 'edit'])->name('plans.edit');
        Route::put('/plans/{plan}', [PlanController::class, 'update'])->name('plans.update');
        Route::delete('/plans/{plan}', [PlanController::class, 'destroy'])->name('plans.destroy');
        Route::patch('/plans/{plan}/toggle-status', [PlanController::class, 'toggleStatus'])->name('plans.toggle-status');

        // Payment Management Routes
        Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
        Route::get('/payments/{payment}', [PaymentController::class, 'show'])->name('payments.show');
        Route::match(['put', 'patch'], '/payments/{payment}', [PaymentController::class, 'update'])->name('payments.update');

        // API Token Management Routes
        Route::get('/api-tokens', [ApiTokenController::class, 'index'])->name('api-tokens.index');
        Route::get('/api-tokens/{id}', [ApiTokenController::class, 'show'])->name('api-tokens.show');
        Route::delete('/api-tokens/{id}', [ApiTokenController::class, 'destroy'])->name('api-tokens.destroy');
        Route::get('/api-tokens-stats', [ApiTokenController::class, 'getStats'])->name('api-tokens.stats');

        // Tawk.to Chat Settings
        // Route::get('/tawk/settings', [TawkSettingsController::class, 'index'])->name('tawk.settings');
        // Route::post('/tawk/settings', [TawkSettingsController::class, 'update'])->name('tawk.settings.update');
        // Route::get('/tawk/test', [TawkSettingsController::class, 'test'])->name('tawk.test');

        // Testimonial Management Routes
        Route::resource('testimonials', TestimonialController::class);
        Route::patch('testimonials/{testimonial}/toggle-status', [TestimonialController::class, 'toggleStatus'])->name('testimonials.toggle-status');
        Route::post('testimonials/update-order', [TestimonialController::class, 'updateOrder'])->name('testimonials.update-order');
    });
});

// // CourierDict Routes
// Route::resource('courier-dictionary', \App\Http\Controllers\Admin\CourierDictController::class);
// Route::get('courier-dictionary-search', [\App\Http\Controllers\Admin\CourierDictController::class, 'search'])->name('courier-dictionary.search');
// Route::post('courier-dictionary/restore/{id}', [\App\Http\Controllers\Admin\CourierDictController::class, 'restore'])->name('courier-dictionary.restore');
// Route::delete('courier-dictionary/force-delete/{id}', [\App\Http\Controllers\Admin\CourierDictController::class, 'forceDelete'])->name('courier-dictionary.force-delete');
// Route::get('courier-dictionary/trashed', [\App\Http\Controllers\Admin\CourierDictController::class, 'trashed'])->name('courier-dictionary.trashed');

Route::post('/admin/blog/upload-image', [BlogController::class, 'uploadImage'])
    ->name('admin.blog.upload-image');
