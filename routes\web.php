<?php

use App\Http\Controllers\Tools\CoordinatesDistanceController;
use App\Http\Controllers\Tools\PinToPinDistanceController;
use App\Http\Controllers\ApiDocController;
use App\Http\Controllers\BlogPostController;
use App\Http\Controllers\CookieConsentController;
use App\Http\Controllers\CourierDictController;
use App\Http\Controllers\DigipinToolController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\PincodeDirectoryController;
use App\Http\Controllers\PincodeDistanceController;
use App\Http\Controllers\PincodeGeoController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\ToolReviewController;
use App\Http\Controllers\ToolsController;
use Illuminate\Support\Facades\Route;

Route::get('/', [SiteController::class, 'index'])->name('home');
Route::get('/search', [PincodeDirectoryController::class, 'searchPincode'])->name('search');

// Pages
Route::post('/cookies-consent', [CookieConsentController::class, 'update'])->name('cookie-consent.update');
Route::get('/pages/contact-us', [SiteController::class, 'contact'])->name('pages.contact');
Route::get('/pages/about-us', [SiteController::class, 'about'])->name('pages.about');
Route::get('/pages/{slug}', [SiteController::class, 'pageShow'])->name('pages.show');
Route::post('/contact-submit', [SiteController::class, 'submitContact'])->name('contact.submit');
Route::post('/contact/change/{name}', [PincodeDirectoryController::class, 'changeContactNumber'])->name('contact.change');

Route::get('pincodes/api-documentation', [ApiDocController::class, 'index'])->name('api.docs.index');
Route::get('/plans', [PlanController::class, 'publicPlans'])->name('plans.public');



// Pincode routes (public)
Route::controller(PincodeDirectoryController::class)->group(function () {
    Route::post('/reviews', 'storeReviews')->name('reviews.store');
    Route::get('/reviews/{pincode_id}', 'viewAllReviews')->name('reviews.all');
    Route::post('/likes', 'storeLikes')->name('likes.store');

    // Pincode Directory
    Route::get('/india-postal-code-list', 'allPincodelist');
    Route::get('/pincodes', 'listofstates')->name('pincodes.states');
    Route::get('/all-pincodes', 'allPincodelist')->name('pincodes.all');
    Route::get('/pincodes/{state}', 'listofdistricts')->where('state', '[A-Za-z\s]+')->name('pincodes.districts');
    Route::get('/pincodes/{state}/{district}', 'listofpostoffices')->where('district', '[A-Za-z\s\.\-0-9\s.-]+')->name('pincodes.postoffices');
    Route::get('/pincodes/{state}/{district}/{name}', 'searchByStateDistrictAndName')
        ->where('name', "[A-Za-z\s\.\-',0-9\(\)]+")
        ->name('pincodes.details-by-name');

    // Route::get('/pincodes/{state}/{district}/PIN/{pincode}', function ($state, $district, $pincode) {
    //     return redirect("/pincodes/{$state}/{$district}/pin/{$pincode}", 301);
    // })->where('pincode', '[0-9]+');

    Route::get('/pincodes/{state}/{district}/postal-code/{pincode}', 'searchByStateDistrictAndPincode')
        ->where('pincode', '[0-9]+')
        ->name('pincodes.pincode');
});

// Courier Dictionary [public]
Route::get('/courier-dictionary', [CourierDictController::class, 'index'])->name('courier_dict.index');
Route::get('/dictionary/search', [CourierDictController::class, 'search'])->name('courier_dict.search');
Route::get('/courier-dictionary/{slug}', [CourierDictController::class, 'show'])->name('courier_dict.show');
Route::get('/api/dict/autocomplete', [CourierDictController::class, 'autocomplete'])->name('courier_dict.autocomplete');

// ::::::::::::::::::::::::::::::::::::::::::::::::::::::
// Blog and Comments [public]
Route::controller(BlogPostController::class)->group(function () {
    Route::get('/blog', 'index')->name('blog.index');
    Route::get('/blog/{slug}', 'show')->name('blog.show');
    Route::get('/blog/category/{slug}', 'viewCategory')->name('blog.category');
    Route::get('/blog/tag/{slug}', 'viewTag')->name('blog.tag');
    Route::post('/posts/{post}/tags', 'manageTags')->name('blog.tags.manage');
    Route::get('/api/blog/autocomplete', 'autocomplete')->name('blog.autocomplete');
});

// Blog Comments [user auth]
Route::group(['middleware' => ['auth']], function () {
    Route::post('/posts/{post}/comments', [BlogPostController::class, 'storeComment'])
        ->name('comments.store');
    Route::patch('/comments/{comment}', [BlogPostController::class, 'updateComment'])
        ->name('comments.update');
    Route::delete('/comments/{comment}', [BlogPostController::class, 'deleteComment'])
        ->name('comments.delete');
});
// ::::::::::::::::::::::::::::::::::::::::::::::::::::::

// ::::::::::::::::::::::::::::::::::::::::::::::::::::::
// Location routes
Route::controller(LocationController::class)->group(function () {
    /**
     * siebar search
     * district wise pincode download
     * pincode address search
     */
    Route::get('get-districts/{state_id}', 'getDistricts');
    Route::post('/post-office-search', 'search');

    // Route::get('/autocomplete-search', 'autoSearch')->name('autocomplete.search');
    // Route::post('/pincode-search', 'pincodeSearch');
    // Route::get('/download-pincodes', 'downloadPincodes')->name('download.pincodes');

    // Doistrict wise Pincode Download Routes
    Route::post('get-downloadable-data', 'getDownloadableData')->name('getdownloadabledata.pincodes');
    Route::post('download-now', 'downloadNow')->name('pincodes.downloadNow');
    // Route::get('/download/{state}/pincode-list-{district}', 'downloadPincodesByDistrict')->name('download.pincodesbyDistrict');

    // Pincode of my location
    Route::get('/nearest-post-offices/{latitude}/{longitude}', 'getNearestPostOfficeLocation');

    // Route::post('/post-office-search', 'search')
    //     ->name('post.office.search')
    //     ->middleware(['web']);
});
// ::::::::::::::::::::::::::::::::::::::::::::::::::::::

// ::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
// Tools routes
Route::get('/tools', [ToolsController::class, 'index'])->name('tools.index');

// Route::get('pincode-of-my-location', function () {
//     return redirect('/tools/pincode-of-my-location', 301);
// })->name('pincode.MyLocation');
Route::get('/tools/{slug}', [ToolsController::class, 'showTool'])->name('tools.show');
Route::post('/tools/{tool}/reviews', [ToolReviewController::class, 'store'])->name('tools.reviews.store');
Route::get('/tools/{slug}/reviews', [ToolReviewController::class, 'viewReviews'])->name('tools.reviews.view');

// distance calculation using map coordinates
Route::post('/calculate-distance-coordinates', [CoordinatesDistanceController::class, 'calculateDistance'])->name('calculate.distance.coordinates');

// pin to pin distance calculation
Route::post('/validate-pincode', [PinToPinDistanceController::class, 'validatePincode'])->name('validate.pincode');
Route::post('/calculate-pin-to-pin-distance', [PinToPinDistanceController::class, 'calculateDistance'])->name('calculate.distance.pincodes');
// ::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::

// Bulk Pincode Distance Calculator
Route::get('/pincode', [PincodeDistanceController::class, 'index'])->name('pincode.index');
Route::post('/pincode/upload', [PincodeDistanceController::class, 'upload'])->name('pincode.upload');
Route::get('/pincode/download/excel', [PincodeDistanceController::class, 'downloadExcel'])->name('pincode.download.excel');
Route::get('/pincode/download/pdf', [PincodeDistanceController::class, 'downloadPdf'])->name('pincode.download.pdf');

// API route for nearest post office
// Route::get('/api/nearest-post-office/{latitude}/{longitude}', [PincodeDistanceController::class, 'getNearestPostOfficeLocation']);

// Test route to verify 404 handling
Route::get('/test-404', function () {
    abort(404);
    // return view("error.404");
});

require __DIR__ . '/auth.php';
require __DIR__ . '/admin.php';
require __DIR__ . '/user.php';

Route::get('/pincode-geo-home', [PincodeGeoController::class, 'index'])->name('pincodes-geo.index');

Route::get('pincode-geo/nearest', [\App\Http\Controllers\Tools\PincodeBoundaryController::class, 'findPincodeByCoordinates']);
Route::get('pincode-geo/boundary/{pincode}', [\App\Http\Controllers\Tools\PincodeBoundaryController::class, 'getBoundaryByPincode']);

Route::get('pincode-geo/search', [PincodeGeoController::class, 'search']);
Route::get('pincode-geo/bounds', [PincodeGeoController::class, 'getPincodesByBounds']);
Route::get('pincode-geo/post-offices', [PincodeGeoController::class, 'searchPostOfficesByPincode']);
Route::get('pincode-geo/{pincode}', [PincodeGeoController::class, 'show']);
