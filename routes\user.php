<?php

use App\Http\Controllers\User\OrderController;
use App\Http\Controllers\User\ApiTokenController;
use App\Http\Controllers\User\PaymentController;
use App\Http\Controllers\PincodeDirectoryController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\User\ProfileController;
use App\Http\Controllers\User\DashboardController;

Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::post('/change-contact-number/{name}', [PincodeDirectoryController::class, 'changeContactNumber'])->name('contact-number-change');
});

Route::middleware('auth')->group(function () {
    Route::get('/api-usage', [PincodeDirectoryController::class, 'edit'])->name('api-usage');

    // API Token routes
    Route::get('/api-tokens', [ApiTokenController::class, 'index'])->name('user.api-tokens.index');
    Route::get('/api-tokens/create', [ApiTokenController::class, 'create'])->name('user.api-tokens.create');
    Route::post('api-token-store', [ApiTokenController::class, 'store'])->name('tokens.store');
    Route::post('api-token/regenerate/{tokenId}', [ApiTokenController::class, 'regenerate'])->name('tokens.regenerate');
    Route::delete('api-token-delete/{token_id}', [ApiTokenController::class, 'destroy'])->name('tokens.destroy');
    Route::post('api-token-revoke-all',[ApiTokenController::class, 'revokeAll'])->name('tokens.revoke-all');
});

// Order routes for users
Route::middleware('auth')->group(function () {
    Route::get('/orders', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/create', [OrderController::class, 'create'])->name('orders.create');
    Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
    Route::get('/orders/{order}', [OrderController::class, 'show'])->name('orders.show');
    Route::patch('/orders/{order}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
    Route::delete('/orders/{order}', [OrderController::class, 'destroy'])->name('orders.destroy');
});


// PayPal Routes
Route::middleware('auth')->group(function () {
    Route::get('/payment/process/{order}', [PaymentController::class, 'process'])->name('payment.process');
    Route::post('/payment/create', [PaymentController::class, 'createPayment'])->name('payment.create');
    
    // Route::view('/payment', 'payment.form')->name('payment.form');
    Route::view('/payment/success-page', 'user.payment.success')->name('payment.success.page');
    Route::view('/payment/cancel-page', 'user.payment.cancel')->name('payment.cancel.page');
    
    Route::get('/payment/success', [PaymentController::class, 'success'])->name('payment.success');
    Route::get('/payment/cancel', [PaymentController::class, 'cancel'])->name('payment.cancel');
    Route::get('/user/plans', [PlanController::class, 'index'])->name('user.plans.index');
});




// Test PayPal credentials (remove in production)
// Route::get('/test-paypal', function () {
//     try {
//         $provider = new \Srmklive\PayPal\Services\PayPal;
//         $provider->setApiCredentials(config('paypal'));
//         $token = $provider->getAccessToken();

//         return response()->json([
//             'success' => true,
//             'token_type' => $token['token_type'] ?? 'none',
//             'expires_in' => $token['expires_in'] ?? 0,
//             'config' => [
//                 'mode' => config('paypal.mode'),
//                 'has_client_id' => !empty(config('paypal.sandbox.client_id')),
//                 'has_client_secret' => !empty(config('paypal.sandbox.client_secret')),
//             ]
//         ]);
//     } catch (\Exception $e) {
//         return response()->json([
//             'success' => false,
//             'message' => $e->getMessage(),
//             'config' => [
//                 'mode' => config('paypal.mode'),
//                 'has_client_id' => !empty(config('paypal.sandbox.client_id')),
//                 'has_client_secret' => !empty(config('paypal.sandbox.client_secret')),
//             ]
//         ], 500);
//     }
// });

// Mock payment route (for testing only - remove in production)
// Route::post('/payment/mock', [PaymentController::class, 'mockPayment'])
//     ->name('payment.mock')
//     ->middleware(['auth']);