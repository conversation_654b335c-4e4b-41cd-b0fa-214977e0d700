@php
    $isHome = request()->routeIs('home');
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <link rel="icon" href="{{ uploads_url(get_setting('site_favicon')) }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />
    <link href="https://fonts.bunny.net/css?family=poppins:400,500,600,700&display=swap" rel="stylesheet" />

    <script>
        (function() {
            try {
                var theme = localStorage.getItem('theme');
                if (theme === 'dark' || (!theme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    document.documentElement.setAttribute('data-theme', 'light');
                }
            } catch (e) {}
        })();
    </script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')

    <!-- Theme CSS - Load after Vite assets to override Tailwind -->
    {{-- <link rel="stylesheet" href="{{ asset('css/theme.css') }}?v={{ filemtime(public_path('css/theme.css')) }}"> --}}

    @yield('json-ld')

    {{-- Auto Ads --}}
    {{-- <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3298502064453559"
        crossorigin="anonymous"></script> --}}

    <!-- Keep meta tags for potential future use -->
    {{-- <meta name="typography-primary-font" content="{{ get_setting('typography_primary_font', 'Inter, sans-serif') }}">
    <meta name="typography-secondary-font"
        content="{{ get_setting('typography_secondary_font', 'Roboto, sans-serif') }}">
    <meta name="typography-h1-size" content="{{ get_setting('typography_h1_size', '2.5rem') }}">
    <meta name="typography-h2-size" content="{{ get_setting('typography_h2_size', '2rem') }}"> --}}
</head>

<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100 dark:bg-gray-900">
        @include('layouts.navigation')

        <!-- Page Heading -->
        {{-- @isset($header)
            <header class="bg-white dark:bg-gray-800 shadow-sm">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    {{ $header }}
    </div>
    </header>
    @endisset --}}

        <!-- Page Content -->
        <main class="{{ $isHome ? '' : 'pt-16' }}">
            {{ $slot ?? '' }}
            @yield('content')
        </main>

        @include('layouts.footer')

        <!-- Include Tawk.to Chat Component -->
        {{-- @if (get_setting('tawk_to_chat_enabled', 'false'))
            <x-tawk-chat />
        @endif --}}
    </div>

    @if (get_setting('is_cookie_consent_enabled', 'true'))
        <x-cookie-consent />
    @endif

    @stack('scripts')

    <!-- Theme Toggle Script -->
    <script>
        // Theme toggle functionality
        function setThemeIcon(isDark) {
            const icon = isDark ? '☀️' : '🌙';
            const navIcon = document.getElementById('theme-switcher-icon');
            const mobileIcon = document.getElementById('theme-switcher-icon-mobile');
            if (navIcon) navIcon.textContent = icon;
            if (mobileIcon) mobileIcon.textContent = icon;
        }

        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            if (isDark) {
                html.classList.remove('dark');
                html.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                html.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            }
            setThemeIcon(!isDark);
            // Optionally, still send AJAX to server if you want to track theme server-side
        }
        // Add theme toggle button to page (for floating button, optional)
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial icon state for nav and mobile theme switchers
            setThemeIcon(document.documentElement.classList.contains('dark'));
            // Optionally, keep the floating button for quick access
            // const themeToggle = document.createElement('button');
            // themeToggle.innerHTML = '🌙';
            // themeToggle.className =
            //     'fixed bottom-4 right-4 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-theme';
            // themeToggle.onclick = toggleTheme;
            // document.body.appendChild(themeToggle);
        });
    </script>
</body>

</html>
