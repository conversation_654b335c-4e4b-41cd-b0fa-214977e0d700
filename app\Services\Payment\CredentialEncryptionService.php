<?php

namespace App\Services\Payment;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Encryption\DecryptException;

class CredentialEncryptionService
{
    /**
     * Sensitive fields that should be encrypted
     */
    private const SENSITIVE_FIELDS = [
        'key_secret',
        'client_secret',
        'webhook_secret',
        'private_key',
        'api_secret',
        'secret_key',
        'password',
        'token',
        'access_token',
        'refresh_token'
    ];

    /**
     * Encrypt sensitive configuration data
     *
     * @param array $configuration
     * @return array
     */
    public function encryptConfiguration(array $configuration): array
    {
        $encrypted = [];

        foreach ($configuration as $key => $value) {
            if ($this->isSensitiveField($key)) {
                $encrypted[$key] = $this->encryptValue($value);
            } else {
                $encrypted[$key] = $value;
            }
        }

        return $encrypted;
    }

    /**
     * Decrypt sensitive configuration data
     *
     * @param array $configuration
     * @return array
     */
    public function decryptConfiguration(array $configuration): array
    {
        $decrypted = [];

        foreach ($configuration as $key => $value) {
            if ($this->isSensitiveField($key)) {
                $decrypted[$key] = $this->decryptValue($value);
            } else {
                $decrypted[$key] = $value;
            }
        }

        return $decrypted;
    }

    /**
     * Encrypt a single value
     *
     * @param mixed $value
     * @return string|null
     */
    public function encryptValue($value): ?string
    {
        if (empty($value)) {
            return null;
        }

        try {
            return Crypt::encryptString((string) $value);
        } catch (\Exception $e) {
            Log::error('Failed to encrypt credential value', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new \RuntimeException('Failed to encrypt credential value');
        }
    }

    /**
     * Decrypt a single value
     *
     * @param string|null $encryptedValue
     * @return string|null
     */
    public function decryptValue(?string $encryptedValue): ?string
    {
        if (empty($encryptedValue)) {
            return null;
        }

        try {
            return Crypt::decryptString($encryptedValue);
        } catch (DecryptException $e) {
            Log::error('Failed to decrypt credential value', [
                'error' => $e->getMessage(),
                'encrypted_value_length' => strlen($encryptedValue)
            ]);
            
            // Return null for invalid encrypted values instead of throwing
            // This allows for graceful handling of corrupted data
            return null;
        } catch (\Exception $e) {
            Log::error('Unexpected error during credential decryption', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return null;
        }
    }

    /**
     * Check if a field is sensitive and should be encrypted
     *
     * @param string $fieldName
     * @return bool
     */
    public function isSensitiveField(string $fieldName): bool
    {
        $fieldName = strtolower($fieldName);
        
        foreach (self::SENSITIVE_FIELDS as $sensitiveField) {
            if (str_contains($fieldName, $sensitiveField)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Mask sensitive value for display purposes
     *
     * @param string|null $value
     * @param int $visibleChars
     * @return string
     */
    public function maskValue(?string $value, int $visibleChars = 4): string
    {
        if (empty($value)) {
            return 'Not set';
        }

        $length = strlen($value);
        
        if ($length <= $visibleChars) {
            return str_repeat('*', $length);
        }

        $visible = substr($value, 0, $visibleChars);
        $masked = str_repeat('*', $length - $visibleChars);
        
        return $visible . $masked;
    }

    /**
     * Validate encrypted configuration integrity
     *
     * @param array $configuration
     * @return array Array of validation results
     */
    public function validateConfiguration(array $configuration): array
    {
        $results = [
            'valid' => true,
            'errors' => [],
            'warnings' => []
        ];

        foreach ($configuration as $key => $value) {
            if ($this->isSensitiveField($key)) {
                if (empty($value)) {
                    $results['warnings'][] = "Sensitive field '{$key}' is empty";
                    continue;
                }

                // Try to decrypt to validate integrity
                $decrypted = $this->decryptValue($value);
                if ($decrypted === null) {
                    $results['valid'] = false;
                    $results['errors'][] = "Failed to decrypt field '{$key}' - data may be corrupted";
                }
            }
        }

        return $results;
    }

    /**
     * Generate a secure random key for testing purposes
     *
     * @param int $length
     * @return string
     */
    public function generateTestKey(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Securely compare two credential values
     *
     * @param string|null $value1
     * @param string|null $value2
     * @return bool
     */
    public function secureCompare(?string $value1, ?string $value2): bool
    {
        if ($value1 === null && $value2 === null) {
            return true;
        }

        if ($value1 === null || $value2 === null) {
            return false;
        }

        return hash_equals($value1, $value2);
    }

    /**
     * Create a hash of configuration for integrity checking
     *
     * @param array $configuration
     * @return string
     */
    public function createConfigurationHash(array $configuration): string
    {
        // Sort configuration to ensure consistent hashing
        ksort($configuration);
        
        return hash('sha256', serialize($configuration));
    }

    /**
     * Verify configuration integrity using hash
     *
     * @param array $configuration
     * @param string $expectedHash
     * @return bool
     */
    public function verifyConfigurationIntegrity(array $configuration, string $expectedHash): bool
    {
        $currentHash = $this->createConfigurationHash($configuration);
        return hash_equals($expectedHash, $currentHash);
    }

    /**
     * Rotate encryption for existing credentials
     *
     * @param array $configuration
     * @return array
     */
    public function rotateEncryption(array $configuration): array
    {
        // First decrypt with old key, then encrypt with new key
        $decrypted = $this->decryptConfiguration($configuration);
        return $this->encryptConfiguration($decrypted);
    }

    /**
     * Export configuration for backup (with encryption)
     *
     * @param array $configuration
     * @return array
     */
    public function exportConfiguration(array $configuration): array
    {
        return [
            'encrypted_data' => base64_encode(json_encode($configuration)),
            'hash' => $this->createConfigurationHash($configuration),
            'exported_at' => now()->toISOString(),
            'version' => '1.0'
        ];
    }

    /**
     * Import configuration from backup
     *
     * @param array $exportedData
     * @return array
     * @throws \InvalidArgumentException
     */
    public function importConfiguration(array $exportedData): array
    {
        if (!isset($exportedData['encrypted_data'], $exportedData['hash'])) {
            throw new \InvalidArgumentException('Invalid export data format');
        }

        $configuration = json_decode(base64_decode($exportedData['encrypted_data']), true);
        
        if (!$configuration) {
            throw new \InvalidArgumentException('Failed to decode configuration data');
        }

        // Verify integrity
        if (!$this->verifyConfigurationIntegrity($configuration, $exportedData['hash'])) {
            throw new \InvalidArgumentException('Configuration integrity check failed');
        }

        return $configuration;
    }

    /**
     * Clean up sensitive data from memory
     *
     * @param array &$data
     */
    public function cleanupSensitiveData(array &$data): void
    {
        foreach ($data as $key => &$value) {
            if ($this->isSensitiveField($key) && is_string($value)) {
                // Overwrite memory with random data
                $length = strlen($value);
                $value = str_repeat("\0", $length);
                unset($data[$key]);
            }
        }
    }
}