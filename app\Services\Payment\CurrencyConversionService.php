<?php

namespace App\Services\Payment;

use App\Models\CurrencyRate;
use App\Models\PaymentGateway;
use App\Services\Payment\Exceptions\PaymentGatewayException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CurrencyConversionService
{
    private const CACHE_TTL = 3600; // 1 hour
    private const API_CACHE_TTL = 21600; // 6 hours
    
    /**
     * Convert amount from one currency to another.
     */
    public function convert(float $amount, string $fromCurrency, string $toCurrency): float
    {
        if (strtoupper($fromCurrency) === strtoupper($toCurrency)) {
            return $amount;
        }

        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            throw new PaymentGatewayException(
                "Exchange rate not available for {$fromCurrency} to {$toCurrency}"
            );
        }

        return round($amount * $rate, 2);
    }

    /**
     * Get exchange rate between two currencies.
     */
    public function getExchangeRate(string $fromCurrency, string $toCurrency): ?float
    {
        $fromCurrency = strtoupper($fromCurrency);
        $toCurrency = strtoupper($toCurrency);

        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        // Try to get rate from database first
        $rate = CurrencyRate::getRateWithFallback($fromCurrency, $toCurrency);
        
        if ($rate !== null) {
            return $rate;
        }

        // Try to fetch from external API if enabled
        if ($this->shouldFetchFromAPI($fromCurrency, $toCurrency)) {
            return $this->fetchRateFromAPI($fromCurrency, $toCurrency);
        }

        return null;
    }

    /**
     * Convert price for a specific payment gateway.
     */
    public function convertForGateway(float $amount, string $fromCurrency, PaymentGateway $gateway): array
    {
        $gatewayCurrency = $this->getGatewayCurrency($gateway);
        
        if (strtoupper($fromCurrency) === strtoupper($gatewayCurrency)) {
            return [
                'original_amount' => $amount,
                'original_currency' => strtoupper($fromCurrency),
                'converted_amount' => $amount,
                'converted_currency' => strtoupper($gatewayCurrency),
                'exchange_rate' => 1.0,
                'conversion_needed' => false,
            ];
        }

        $convertedAmount = $this->convert($amount, $fromCurrency, $gatewayCurrency);
        $exchangeRate = $this->getExchangeRate($fromCurrency, $gatewayCurrency);

        return [
            'original_amount' => $amount,
            'original_currency' => strtoupper($fromCurrency),
            'converted_amount' => $convertedAmount,
            'converted_currency' => strtoupper($gatewayCurrency),
            'exchange_rate' => $exchangeRate,
            'conversion_needed' => true,
        ];
    }

    /**
     * Get supported currencies.
     */
    public function getSupportedCurrencies(): array
    {
        return CurrencyRate::getSupportedCurrencies()->toArray();
    }

    /**
     * Check if conversion is supported between two currencies.
     */
    public function isConversionSupported(string $fromCurrency, string $toCurrency): bool
    {
        return CurrencyRate::isConversionSupported($fromCurrency, $toCurrency);
    }

    /**
     * Update exchange rates from external API.
     */
    public function updateRatesFromAPI(array $currencies = null): array
    {
        $currencies = $currencies ?? $this->getSupportedCurrencies();
        $updated = [];
        $errors = [];

        foreach ($currencies as $baseCurrency) {
            try {
                $rates = $this->fetchMultipleRatesFromAPI($baseCurrency, $currencies);
                
                foreach ($rates as $targetCurrency => $rate) {
                    if ($baseCurrency !== $targetCurrency && $rate > 0) {
                        CurrencyRate::setRate($baseCurrency, $targetCurrency, $rate, CurrencyRate::SOURCE_API);
                        $updated[] = "{$baseCurrency} -> {$targetCurrency}: {$rate}";
                    }
                }

            } catch (\Exception $e) {
                $errors[] = "Failed to update rates for {$baseCurrency}: " . $e->getMessage();
                Log::error('Currency rate update failed', [
                    'base_currency' => $baseCurrency,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        Log::info('Currency rates updated from API', [
            'updated_count' => count($updated),
            'error_count' => count($errors),
        ]);

        return [
            'updated' => $updated,
            'errors' => $errors,
            'total_updated' => count($updated),
            'total_errors' => count($errors),
        ];
    }

    /**
     * Get currency conversion preview for multiple gateways.
     */
    public function getConversionPreview(float $amount, string $fromCurrency): array
    {
        $preview = [];
        $activeGateways = PaymentGateway::getActiveGateways();

        foreach ($activeGateways as $gateway) {
            try {
                $conversion = $this->convertForGateway($amount, $fromCurrency, $gateway);
                
                $preview[$gateway->name] = [
                    'gateway' => $gateway->display_name,
                    'original_amount' => $conversion['original_amount'],
                    'converted_amount' => $conversion['converted_amount'],
                    'currency' => $conversion['converted_currency'],
                    'exchange_rate' => $conversion['exchange_rate'],
                    'conversion_needed' => $conversion['conversion_needed'],
                    'formatted_amount' => $this->formatAmount($conversion['converted_amount'], $conversion['converted_currency']),
                ];

            } catch (\Exception $e) {
                $preview[$gateway->name] = [
                    'gateway' => $gateway->display_name,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $preview;
    }

    /**
     * Calculate conversion fees (if any).
     */
    public function calculateConversionFee(float $amount, string $fromCurrency, string $toCurrency): float
    {
        // Most payment gateways don't charge separate conversion fees
        // This can be customized based on business requirements
        if (strtoupper($fromCurrency) === strtoupper($toCurrency)) {
            return 0.0;
        }

        // Example: 0.5% conversion fee for cross-currency transactions
        return round($amount * 0.005, 2);
    }

    /**
     * Get historical exchange rates.
     */
    public function getHistoricalRates(string $fromCurrency, string $toCurrency, int $days = 30): array
    {
        // This would typically fetch from a time-series database or API
        // For now, return current rate as placeholder
        $currentRate = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        $rates = [];
        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            // Add some random variation for demo purposes
            $variation = 1 + (rand(-100, 100) / 10000); // ±1% variation
            $rates[$date] = round($currentRate * $variation, 6);
        }

        return $rates;
    }

    /**
     * Get currency rate age and freshness.
     */
    public function getRateFreshness(string $fromCurrency, string $toCurrency): array
    {
        $rate = CurrencyRate::where('from_currency', strtoupper($fromCurrency))
                          ->where('to_currency', strtoupper($toCurrency))
                          ->latest()
                          ->first();

        if (!$rate) {
            return [
                'exists' => false,
                'age_hours' => null,
                'is_stale' => true,
                'source' => null,
                'last_updated' => null,
            ];
        }

        $ageHours = $rate->getAgeInHours();
        
        return [
            'exists' => true,
            'age_hours' => $ageHours,
            'is_stale' => $rate->isStale(24), // Consider stale after 24 hours
            'source' => $rate->source,
            'last_updated' => $rate->updated_at->toISOString(),
        ];
    }

    /**
     * Bulk convert multiple amounts.
     */
    public function bulkConvert(array $amounts, string $fromCurrency, string $toCurrency): array
    {
        $rate = $this->getExchangeRate($fromCurrency, $toCurrency);
        
        if ($rate === null) {
            throw new PaymentGatewayException(
                "Exchange rate not available for {$fromCurrency} to {$toCurrency}"
            );
        }

        $results = [];
        foreach ($amounts as $key => $amount) {
            $results[$key] = [
                'original' => $amount,
                'converted' => round($amount * $rate, 2),
                'rate' => $rate,
            ];
        }

        return $results;
    }

    /**
     * Get gateway's preferred currency.
     */
    private function getGatewayCurrency(PaymentGateway $gateway): string
    {
        $config = $gateway->getCredentials();
        return $config['currency'] ?? 'USD';
    }

    /**
     * Check if we should fetch rate from API.
     */
    private function shouldFetchFromAPI(string $fromCurrency, string $toCurrency): bool
    {
        // Check if API fetching is enabled (could be a config setting)
        if (!config('payment.currency_api_enabled', false)) {
            return false;
        }

        // Check if we recently tried to fetch this rate
        $cacheKey = "currency_api_attempt_{$fromCurrency}_{$toCurrency}";
        return !Cache::has($cacheKey);
    }

    /**
     * Fetch exchange rate from external API.
     */
    private function fetchRateFromAPI(string $fromCurrency, string $toCurrency): ?float
    {
        $cacheKey = "currency_api_attempt_{$fromCurrency}_{$toCurrency}";
        Cache::put($cacheKey, true, 300); // Prevent frequent API calls

        try {
            // Example using a free currency API (replace with your preferred provider)
            $apiKey = config('payment.currency_api_key');
            if (!$apiKey) {
                return null;
            }

            $response = Http::timeout(10)
                          ->get("https://api.exchangerate-api.com/v4/latest/{$fromCurrency}");

            if ($response->successful()) {
                $data = $response->json();
                $rate = $data['rates'][$toCurrency] ?? null;

                if ($rate) {
                    // Store the rate in database
                    CurrencyRate::setRate($fromCurrency, $toCurrency, $rate, CurrencyRate::SOURCE_API);
                    
                    Log::info('Exchange rate fetched from API', [
                        'from' => $fromCurrency,
                        'to' => $toCurrency,
                        'rate' => $rate,
                    ]);

                    return $rate;
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to fetch exchange rate from API', [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'error' => $e->getMessage(),
            ]);
        }

        return null;
    }

    /**
     * Fetch multiple rates from API for efficiency.
     */
    private function fetchMultipleRatesFromAPI(string $baseCurrency, array $targetCurrencies): array
    {
        try {
            $apiKey = config('payment.currency_api_key');
            if (!$apiKey) {
                return [];
            }

            $response = Http::timeout(15)
                          ->get("https://api.exchangerate-api.com/v4/latest/{$baseCurrency}");

            if ($response->successful()) {
                $data = $response->json();
                $rates = [];

                foreach ($targetCurrencies as $currency) {
                    if (isset($data['rates'][$currency])) {
                        $rates[$currency] = $data['rates'][$currency];
                    }
                }

                return $rates;
            }

        } catch (\Exception $e) {
            Log::error('Failed to fetch multiple exchange rates from API', [
                'base' => $baseCurrency,
                'targets' => $targetCurrencies,
                'error' => $e->getMessage(),
            ]);
        }

        return [];
    }

    /**
     * Format amount with currency symbol.
     */
    private function formatAmount(float $amount, string $currency): string
    {
        $symbols = [
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'INR' => '₹',
            'JPY' => '¥',
        ];

        $symbol = $symbols[$currency] ?? $currency . ' ';
        
        if (in_array($currency, ['USD', 'EUR', 'GBP'])) {
            return $symbol . number_format($amount, 2);
        }
        
        return number_format($amount, 2) . ' ' . $currency;
    }

    /**
     * Clear all currency conversion cache.
     */
    public function clearCache(): void
    {
        CurrencyRate::clearCache();
        
        // Clear API attempt cache
        $currencies = $this->getSupportedCurrencies();
        foreach ($currencies as $from) {
            foreach ($currencies as $to) {
                if ($from !== $to) {
                    Cache::forget("currency_api_attempt_{$from}_{$to}");
                }
            }
        }

        Log::info('Currency conversion cache cleared');
    }

    /**
     * Get conversion statistics.
     */
    public function getConversionStats(): array
    {
        $totalRates = CurrencyRate::count();
        $apiRates = CurrencyRate::api()->count();
        $manualRates = CurrencyRate::manual()->count();
        $staleRates = CurrencyRate::get()->filter(fn($rate) => $rate->isStale(24))->count();

        return [
            'total_rates' => $totalRates,
            'api_rates' => $apiRates,
            'manual_rates' => $manualRates,
            'stale_rates' => $staleRates,
            'supported_currencies' => count($this->getSupportedCurrencies()),
            'cache_enabled' => true,
        ];
    }
}