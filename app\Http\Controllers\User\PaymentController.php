<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Show available plans
     */
    public function showPlans()
    {
        $plans = Plan::where('is_active', true)->get();
        return view('payment.form', compact('plans'));
    }

    /**
     * Process payment for an existing order
     */
    public function process(Order $order)
    {
        // Ensure user can only process their own orders
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        // Only allow processing of pending orders
        if ($order->status !== Order::STATUS_PENDING) {
            return redirect()->route('orders.show', $order)
                ->with('error', 'This order cannot be processed.');
        }

        return view('user.payment.process', compact('order'));
    }

    /**
     * Create a payment for a plan using direct PayPal API calls
     */
    public function createPayment(Request $request)
    {
        try {
            // If order_id is provided, use existing order
            if ($request->has('order_id')) {
                $order = Order::findOrFail($request->order_id);

                // Ensure user can only pay for their own orders
                if ($order->user_id !== Auth::id()) {
                    abort(403);
                }

                // Only allow payment for pending orders
                if ($order->status !== Order::STATUS_PENDING) {
                    return redirect()->route('orders.show', $order)
                        ->with('error', 'This order cannot be processed.');
                }

                $plan = Plan::findOrFail($order->plan_id);
            } else {
                // Create a new order
                $plan = Plan::where('is_active', true)->findOrFail($request->plan_id);

                $order = Order::create([
                    'user_id' => Auth::id(),
                    'plan_id' => $plan->id,
                    'order_number' => 'ORD-' . uniqid(),
                    'amount' => $plan->price,
                    'request_limit' => $plan->request_limit,
                    'status' => Order::STATUS_PENDING,
                ]);
            }

            // Debug information
            \Log::info('Creating PayPal order', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'amount' => $order->amount
            ]);

            // Get PayPal credentials directly from .env
            $mode = env('PAYPAL_MODE', 'sandbox');
            $clientId = $mode === 'sandbox'
                ? env('PAYPAL_SANDBOX_CLIENT_ID', '')
                : env('PAYPAL_LIVE_CLIENT_ID', '');
            $clientSecret = $mode === 'sandbox'
                ? env('PAYPAL_SANDBOX_CLIENT_SECRET', '')
                : env('PAYPAL_LIVE_CLIENT_SECRET', '');

            // Log PayPal config for debugging
            \Log::info('PayPal config', [
                'mode' => $mode,
                'has_client_id' => !empty($clientId),
                'has_client_secret' => !empty($clientSecret),
            ]);

            // Get access token directly
            $tokenUrl = $mode === 'sandbox'
                ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token'
                : 'https://api-m.paypal.com/v1/oauth2/token';

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $tokenUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=client_credentials");
            curl_setopt($ch, CURLOPT_USERPWD, $clientId . ":" . $clientSecret);

            $headers = array();
            $headers[] = 'Accept: application/json';
            $headers[] = 'Accept-Language: en_US';
            $headers[] = 'Content-Type: application/x-www-form-urlencoded';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $result = curl_exec($ch);
            $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_errno($ch)) {
                throw new \Exception('cURL Error: ' . curl_error($ch));
            }

            if ($httpcode != 200) {
                throw new \Exception('Failed to get PayPal token. HTTP Code: ' . $httpcode . ', Response: ' . $result);
            }

            $tokenData = json_decode($result, true);
            $accessToken = $tokenData['access_token'] ?? '';

            if (empty($accessToken)) {
                throw new \Exception('Empty access token received from PayPal');
            }

            // Debug token
            \Log::info('PayPal token obtained', [
                'success' => true,
                'token_type' => $tokenData['token_type'] ?? 'none',
                'expires_in' => $tokenData['expires_in'] ?? 0
            ]);

            // Format the amount properly
            $formattedAmount = number_format((float) $order->amount, 2, '.', '');

            // Create order directly
            $orderUrl = $mode === 'sandbox'
                ? 'https://api-m.sandbox.paypal.com/v2/checkout/orders'
                : 'https://api-m.paypal.com/v2/checkout/orders';

            $orderData = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $order->order_number,
                        'amount' => [
                            'currency_code' => env('PAYPAL_CURRENCY', 'USD'),
                            'value' => $formattedAmount
                        ],
                        'description' => "Payment for {$plan->name} Plan"
                    ]
                ],
                'application_context' => [
                    'return_url' => route('payment.success', ['order_id' => $order->id]),
                    'cancel_url' => route('payment.cancel', ['order_id' => $order->id]),
                    'brand_name' => config('app.name'),
                    'user_action' => 'PAY_NOW',
                    'shipping_preference' => 'NO_SHIPPING'
                ]
            ];

            \Log::info('PayPal order data', ['data' => $orderData]);

            $ch2 = curl_init();
            curl_setopt($ch2, CURLOPT_URL, $orderUrl);
            curl_setopt($ch2, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch2, CURLOPT_POST, 1);
            curl_setopt($ch2, CURLOPT_POSTFIELDS, json_encode($orderData));

            $headers2 = array();
            $headers2[] = 'Content-Type: application/json';
            $headers2[] = 'Authorization: Bearer ' . $accessToken;
            curl_setopt($ch2, CURLOPT_HTTPHEADER, $headers2);

            $result2 = curl_exec($ch2);
            $httpcode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);

            if (curl_errno($ch2)) {
                throw new \Exception('cURL Error: ' . curl_error($ch2));
            }

            $response = json_decode($result2, true);

            // Log the response for debugging
            \Log::info('PayPal create order response', ['response' => $response, 'http_code' => $httpcode2]);

            if ($httpcode2 >= 200 && $httpcode2 < 300 && isset($response['id'])) {
                // Save the PayPal order ID to the database for reference
                $order->update(['paypal_order_id' => $response['id']]);

                // Find the approval URL
                $approveLink = '';
                foreach ($response['links'] as $link) {
                    if ($link['rel'] === 'approve') {
                        $approveLink = $link['href'];
                        break;
                    }
                }

                if (!empty($approveLink)) {
                    return redirect()->away($approveLink);
                }
            }

            throw new \Exception('Failed to create PayPal order: ' . $result2);

        } catch (\Exception $e) {
            \Log::error('PayPal payment error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            if (isset($order)) {
                $order->update(['status' => Order::STATUS_FAILED]);
            }

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment initialization failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle successful payment
     */
    public function success(Request $request)
    {
        try {
            $order = Order::findOrFail($request->order_id);

            // Ensure user can only process their own orders
            if ($order->user_id !== Auth::id()) {
                abort(403);
            }

            // Get PayPal credentials directly from .env
            $mode = env('PAYPAL_MODE', 'sandbox');
            $clientId = $mode === 'sandbox'
                ? env('PAYPAL_SANDBOX_CLIENT_ID', '')
                : env('PAYPAL_LIVE_CLIENT_ID', '');
            $clientSecret = $mode === 'sandbox'
                ? env('PAYPAL_SANDBOX_CLIENT_SECRET', '')
                : env('PAYPAL_LIVE_CLIENT_SECRET', '');

            // Get access token directly
            $tokenUrl = $mode === 'sandbox'
                ? 'https://api-m.sandbox.paypal.com/v1/oauth2/token'
                : 'https://api-m.paypal.com/v1/oauth2/token';

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $tokenUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, "grant_type=client_credentials");
            curl_setopt($ch, CURLOPT_USERPWD, $clientId . ":" . $clientSecret);

            $headers = array();
            $headers[] = 'Accept: application/json';
            $headers[] = 'Accept-Language: en_US';
            $headers[] = 'Content-Type: application/x-www-form-urlencoded';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $result = curl_exec($ch);
            $tokenData = json_decode($result, true);
            $accessToken = $tokenData['access_token'] ?? '';

            if (empty($accessToken)) {
                throw new \Exception('Empty access token received from PayPal');
            }

            // Capture the payment
            $captureUrl = $mode === 'sandbox'
                ? "https://api-m.sandbox.paypal.com/v2/checkout/orders/{$request->token}/capture"
                : "https://api-m.paypal.com/v2/checkout/orders/{$request->token}/capture";

            $ch2 = curl_init();
            curl_setopt($ch2, CURLOPT_URL, $captureUrl);
            curl_setopt($ch2, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch2, CURLOPT_POST, 1);
            curl_setopt($ch2, CURLOPT_POSTFIELDS, "{}");

            $headers2 = array();
            $headers2[] = 'Content-Type: application/json';
            $headers2[] = 'Authorization: Bearer ' . $accessToken;
            curl_setopt($ch2, CURLOPT_HTTPHEADER, $headers2);

            $result2 = curl_exec($ch2);
            $response = json_decode($result2, true);

            \Log::info('PayPal capture response', ['response' => $response]);

            if (isset($response['status']) && $response['status'] == 'COMPLETED') {
                // Get payer information
                $payerEmail = $response['payer']['email_address'] ?? null;

                // Create payment record
                Payment::create([
                    'order_id' => $order->id,
                    'payment_id' => $response['id'],
                    'payer_email' => $payerEmail,
                    'amount' => $order->amount,
                    'currency' => env('PAYPAL_CURRENCY', 'USD'),
                    'payment_status' => $response['status'],
                    'payment_method' => Payment::METHOD_PAYPAL,
                    'payment_details' => $response,
                    'paid_at' => now(),
                ]);

                // Update order status
                $order->update([
                    'status' => Order::STATUS_COMPLETED,
                    'paid_at' => now(),
                ]);

                return redirect()->route('payment.success.page')
                    ->with('success', 'Payment completed successfully!');
            }

            throw new \Exception('Payment not completed. Status: ' . ($response['status'] ?? 'unknown'));

        } catch (\Exception $e) {
            \Log::error('PayPal success callback error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle cancelled payment
     */
    public function cancel(Request $request)
    {
        try {
            $order = Order::findOrFail($request->order_id);

            // Ensure user can only process their own orders
            if ($order->user_id !== Auth::id()) {
                abort(403);
            }

            // Update order status
            $order->update([
                'status' => Order::STATUS_CANCELLED
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('warning', 'Payment was cancelled.');

        } catch (\Exception $e) {
            \Log::error('PayPal cancel callback error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('payment.cancel.page')
                ->with('error', 'Error processing cancellation: ' . $e->getMessage());
        }
    }

    /**
     * Process a mock payment (for testing only)
     */
    // public function mockPayment(Request $request)
    // {
    //     try {
    //         $order = Order::findOrFail($request->order_id);

    //         // Ensure user can only pay for their own orders
    //         if ($order->user_id !== Auth::id()) {
    //             abort(403);
    //         }

    //         // Only allow payment for pending orders
    //         if ($order->status !== Order::STATUS_PENDING) {
    //             return redirect()->route('orders.show', $order)
    //                 ->with('error', 'This order cannot be processed.');
    //         }

    //         // Update order status
    //         $order->update([
    //             'status' => Order::STATUS_COMPLETED,
    //             'paid_at' => now(),
    //         ]);

    //         // Create a success message
    //         session()->flash('success', 'Mock payment completed successfully! Your order has been processed.');

    //         return redirect()->route('dashboard');
    //     } catch (\Exception $e) {
    //         \Log::error('Mock payment error', [
    //             'message' => $e->getMessage(),
    //             'trace' => $e->getTraceAsString()
    //         ]);

    //         return redirect()->route('payment.cancel.page')
    //             ->with('error', 'Mock payment failed: ' . $e->getMessage());
    //     }
    // }
}






