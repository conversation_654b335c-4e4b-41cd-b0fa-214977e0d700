<?php

namespace Tests\Feature\Payment;

use App\Models\Order;
use App\Models\Payment;
use App\Models\PaymentGateway;
use App\Models\WebhookLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class WebhookIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected PaymentGateway $razorpayGateway;
    protected Order $order;
    protected Payment $payment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->razorpayGateway = PaymentGateway::factory()->create([
            'name' => 'razorpay',
            'configuration' => [
                'key_id' => 'rzp_test_1234567890',
                'key_secret' => 'test_secret_key',
                'webhook_secret' => 'test_webhook_secret'
            ],
            'webhook_url' => '/webhook/razorpay',
            'is_active' => true
        ]);

        $user = User::factory()->create();
        $this->order = Order::factory()->create(['user_id' => $user->id]);
        
        $this->payment = Payment::factory()->create([
            'order_id' => $this->order->id,
            'gateway_id' => $this->razorpayGateway->id,
            'gateway_payment_id' => 'pay_test123',
            'gateway_order_id' => 'order_test123',
            'payment_method' => 'razorpay',
            'payment_status' => Payment::STATUS_PENDING
        ]);
    }

    public function test_razorpay_payment_captured_webhook()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'amount' => 100000, // Amount in paise
                        'currency' => 'INR',
                        'status' => 'captured',
                        'method' => 'card',
                        'captured' => true,
                        'created_at' => now()->timestamp
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(200);

        // Verify payment status updated
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_COMPLETED, $this->payment->payment_status);
        $this->assertNotNull($this->payment->paid_at);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals(Order::STATUS_COMPLETED, $this->order->status);

        // Verify webhook log created
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                ->where('payment_id', $this->payment->id)
                                ->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertEquals('payment.captured', $webhookLog->event_type);
        $this->assertEquals('processed', $webhookLog->status);
    }

    public function test_razorpay_payment_failed_webhook()
    {
        $webhookPayload = [
            'event' => 'payment.failed',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'amount' => 100000,
                        'currency' => 'INR',
                        'status' => 'failed',
                        'method' => 'card',
                        'error_code' => 'BAD_REQUEST_ERROR',
                        'error_description' => 'Payment failed due to insufficient funds',
                        'created_at' => now()->timestamp
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(200);

        // Verify payment status updated
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_FAILED, $this->payment->payment_status);
        $this->assertStringContainsString('insufficient funds', $this->payment->failed_reason);

        // Verify order status updated
        $this->order->refresh();
        $this->assertEquals(Order::STATUS_FAILED, $this->order->status);
    }

    public function test_webhook_signature_verification_failure()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $invalidSignature = 'invalid_signature';

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $invalidSignature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(400);

        // Verify payment status not updated
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_PENDING, $this->payment->payment_status);

        // Verify webhook log created with failed status
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                ->where('status', 'failed')
                                ->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertStringContainsString('signature verification failed', $webhookLog->error_message);
    }

    public function test_webhook_for_nonexistent_payment()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_nonexistent',
                        'order_id' => 'order_nonexistent',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(404);

        // Verify webhook log created
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                ->where('status', 'failed')
                                ->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertStringContainsString('Payment not found', $webhookLog->error_message);
    }

    public function test_webhook_duplicate_processing_prevention()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        // First webhook call
        $response1 = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response1->assertStatus(200);

        // Second webhook call (duplicate)
        $response2 = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response2->assertStatus(200); // Should handle gracefully

        // Verify payment status is still correct
        $this->payment->refresh();
        $this->assertEquals(Payment::STATUS_COMPLETED, $this->payment->payment_status);

        // Verify only one successful webhook log
        $successfulLogs = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                   ->where('payment_id', $this->payment->id)
                                   ->where('status', 'processed')
                                   ->count();
        
        $this->assertEquals(1, $successfulLogs);
    }

    public function test_webhook_rate_limiting()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        // Send multiple webhook requests rapidly
        $responses = [];
        for ($i = 0; $i < 10; $i++) {
            $responses[] = $this->withHeaders([
                'X-Razorpay-Signature' => $signature,
                'Content-Type' => 'application/json'
            ])->post('/webhook/razorpay', $webhookPayload);
        }

        // At least some should succeed, but rate limiting might kick in
        $successCount = 0;
        foreach ($responses as $response) {
            if ($response->status() === 200) {
                $successCount++;
            }
        }

        $this->assertGreaterThan(0, $successCount);
    }

    public function test_webhook_retry_mechanism()
    {
        // Create a webhook log with failed status
        $webhookLog = WebhookLog::create([
            'gateway_id' => $this->razorpayGateway->id,
            'payment_id' => $this->payment->id,
            'webhook_id' => 'whook_test123',
            'event_type' => 'payment.captured',
            'payload' => [
                'event' => 'payment.captured',
                'payload' => ['payment' => ['entity' => ['id' => 'pay_test123']]]
            ],
            'status' => 'failed',
            'error_message' => 'Temporary processing error',
            'retry_count' => 0
        ]);

        // Simulate retry
        $response = $this->post("/admin/webhook-monitoring/retry/{$webhookLog->id}");

        $response->assertStatus(200);

        // Verify retry count incremented
        $webhookLog->refresh();
        $this->assertEquals(1, $webhookLog->retry_count);
    }

    public function test_webhook_malformed_payload_handling()
    {
        $malformedPayload = 'invalid json payload';
        $signature = hash_hmac('sha256', $malformedPayload, 'test_webhook_secret');

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->postJson('/webhook/razorpay', [], [], [], $malformedPayload);

        $response->assertStatus(400);

        // Verify webhook log created with error
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                ->where('status', 'failed')
                                ->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertStringContainsString('Invalid JSON payload', $webhookLog->error_message);
    }

    public function test_webhook_unknown_event_handling()
    {
        $webhookPayload = [
            'event' => 'unknown.event',
            'payload' => [
                'data' => ['id' => 'test123']
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(200); // Should handle gracefully

        // Verify webhook log created
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)
                                ->where('event_type', 'unknown.event')
                                ->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertEquals('processed', $webhookLog->status);
    }

    public function test_webhook_security_headers_validation()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        // Missing signature header
        $response = $this->withHeaders([
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(400);

        // Invalid content type
        $signature = hash_hmac('sha256', json_encode($webhookPayload), 'test_webhook_secret');
        
        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'text/plain'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(400);
    }

    public function test_webhook_ip_whitelist_validation()
    {
        // This test would verify IP whitelisting if implemented
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        // Simulate request from non-whitelisted IP
        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json',
            'X-Forwarded-For' => '*************' // Non-whitelisted IP
        ])->post('/webhook/razorpay', $webhookPayload);

        // Depending on implementation, this might be blocked or allowed
        $this->assertTrue(
            $response->status() === 200 || 
            $response->status() === 403
        );
    }

    public function test_webhook_logging_and_monitoring()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        Log::shouldReceive('info')
           ->once()
           ->with('Webhook received', \Mockery::type('array'));

        $response = $this->withHeaders([
            'X-Razorpay-Signature' => $signature,
            'Content-Type' => 'application/json'
        ])->post('/webhook/razorpay', $webhookPayload);

        $response->assertStatus(200);

        // Verify comprehensive webhook log
        $webhookLog = WebhookLog::where('gateway_id', $this->razorpayGateway->id)->first();
        
        $this->assertNotNull($webhookLog);
        $this->assertNotNull($webhookLog->webhook_id);
        $this->assertEquals('payment.captured', $webhookLog->event_type);
        $this->assertNotNull($webhookLog->payload);
        $this->assertNotNull($webhookLog->signature);
        $this->assertEquals('processed', $webhookLog->status);
        $this->assertNotNull($webhookLog->processed_at);
    }

    public function test_webhook_performance_under_load()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test123',
                        'order_id' => 'order_test123',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $payload = json_encode($webhookPayload);
        $signature = hash_hmac('sha256', $payload, 'test_webhook_secret');

        $startTime = microtime(true);

        // Send multiple webhook requests
        $responses = [];
        for ($i = 0; $i < 5; $i++) {
            $responses[] = $this->withHeaders([
                'X-Razorpay-Signature' => $signature,
                'Content-Type' => 'application/json'
            ])->post('/webhook/razorpay', $webhookPayload);
        }

        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;

        // Verify reasonable performance (less than 5 seconds for 5 requests)
        $this->assertLessThan(5.0, $totalTime);

        // Verify all requests were handled
        foreach ($responses as $response) {
            $this->assertTrue($response->status() >= 200 && $response->status() < 500);
        }
    }
}