[2025-07-31 03:22:00] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731032200_YSIvz6j6.jpg","original_name":"payment_proof.jpg","size":8291} 
[2025-07-31 03:22:01] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731032201_JIC3VPOU.jpg","original_name":"test.jpg","size":695} 
[2025-07-31 03:22:02] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731032202_fSyg0pVp.jpg","original_name":"old_file.jpg","size":695} 
[2025-07-31 03:22:03] testing.INFO: Old payment proof files cleaned up {"deleted_count":0,"cutoff_date":"2025-07-30 03:22:02"} 
[2025-07-31 03:22:03] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731032203_fJGTOnuI.jpg","original_name":"test.jpg","size":8291} 
[2025-07-31 04:26:35] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731042635_xDYA7EDs.jpg","original_name":"payment_proof.jpg","size":8291} 
[2025-07-31 04:26:36] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731042636_PK5UAAzE.jpg","original_name":"test.jpg","size":695} 
[2025-07-31 04:26:37] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731042637_U3S6Oq7P.jpg","original_name":"old_file.jpg","size":695} 
[2025-07-31 04:26:37] testing.INFO: Old payment proof files cleaned up {"deleted_count":0,"cutoff_date":"2025-07-30 04:26:37"} 
[2025-07-31 04:26:38] testing.INFO: Payment proof file uploaded securely {"user_id":1,"payment_id":1,"file_path":"payment_proofs/2025/07/user_1/payment_proof_1_20250731042638_hDG164Sv.jpg","original_name":"test.jpg","size":8291} 
[2025-07-31 04:35:21] production.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 1 at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php use App\\\\M...', false)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('use App\\\\Models\\\\...', true)
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('use App\\\\Models\\\\...', true)
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('use App\\\\Models\\\\...')
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-31 04:35:38] production.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php use App\\\\M...', false)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('use App\\\\Models\\\\...', true)
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('use App\\\\Models\\\\...', true)
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('use App\\\\Models\\\\...')
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-31 04:35:56] production.ERROR: PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected T_NS_SEPARATOR on line 1 at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php echo 'Tes...', false)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('echo 'Testing c...', true)
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('echo 'Testing c...', true)
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('echo 'Testing c...')
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#20 {main}
"} 
[2025-07-31 05:37:06] testing.ERROR: Method Mockery_2_Illuminate_Cache_CacheManager::rememberForever() does not exist on this mock object (View: C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views\admin\layouts\admin.blade.php) (View: C:\laragon11\www\pincode-new-1-without-daisy-ui\resources\views\admin\layouts\admin.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): Method Mockery_2_Illuminate_Cache_CacheManager::rememberForever() does not exist on this mock object (View: C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\resources\\views\\admin\\layouts\\admin.blade.php) (View: C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\resources\\views\\admin\\layouts\\admin.blade.php) at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\mockery\\mockery\\library\\Mockery\\Loader\\EvalLoader.php(30) : eval()'d code:973)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 3)
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon11\\\\ww...', Array)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon11\\\\ww...', Array)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\AdminMiddleware.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\CheckMaintenanceMode.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', 'http://localhos...', Array, Array, Array, Array)
#58 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\tests\\Feature\\Admin\\AdminDashboardIntegrationTest.php(57): Illuminate\\Foundation\\Testing\\TestCase->get('http://localhos...')
#59 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->{closure}()
#60 [internal function]: P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Factories\\{closure}()
#61 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#62 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Concerns\\{closure}()
#63 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#64 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__callClosure(Object(Closure), Array)
#65 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(35): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__runTest(Object(Closure))
#66 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__pest_evaluable__Admin_Dashboard_Integration__→__Dashboard_Statistics__→_it_caches_dashboard_statistics()
#67 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#68 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#69 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest))
#70 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#71 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#72 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#73 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#74 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#75 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#76 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#77 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(192): {closure}()
#78 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): Method Mockery_2_Illuminate_Cache_CacheManager::rememberForever() does not exist on this mock object (View: C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\resources\\views\\admin\\layouts\\admin.blade.php) at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\mockery\\mockery\\library\\Mockery\\Loader\\EvalLoader.php(30) : eval()'d code:973)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(60): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Mockery\\Exception\\BadMethodCallException), 4)
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon11\\\\ww...', Array)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon11\\\\ww...', Array)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\storage\\framework\\views\\76ced115c407ef364afe8d47ec4b6695.php(263): Illuminate\\View\\View->render()
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon11\\\\ww...')
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon11\\\\ww...', Array)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon11\\\\ww...', Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon11\\\\ww...', Array)
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\AdminMiddleware.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\CheckMaintenanceMode.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#63 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#64 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#65 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', 'http://localhos...', Array, Array, Array, Array)
#66 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\tests\\Feature\\Admin\\AdminDashboardIntegrationTest.php(57): Illuminate\\Foundation\\Testing\\TestCase->get('http://localhos...')
#67 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->{closure}()
#68 [internal function]: P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Factories\\{closure}()
#69 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#70 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Concerns\\{closure}()
#71 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#72 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__callClosure(Object(Closure), Array)
#73 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(35): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__runTest(Object(Closure))
#74 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__pest_evaluable__Admin_Dashboard_Integration__→__Dashboard_Statistics__→_it_caches_dashboard_statistics()
#75 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#76 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#77 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest))
#78 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#79 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#80 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#81 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#82 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#83 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#84 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#85 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(192): {closure}()
#86 {main}

[previous exception] [object] (Mockery\\Exception\\BadMethodCallException(code: 0): Method Mockery_2_Illuminate_Cache_CacheManager::rememberForever() does not exist on this mock object at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\mockery\\mockery\\library\\Mockery\\Loader\\EvalLoader.php(30) : eval()'d code:973)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\mockery\\mockery\\library\\Mockery\\Loader\\EvalLoader.php(30) : eval()'d code(418): Mockery_2_Illuminate_Cache_CacheManager->_mockery_handleMethodCall('rememberForever', Array)
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Mockery_2_Illuminate_Cache_CacheManager->__call('rememberForever', Array)
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Models\\Setting.php(35): Illuminate\\Support\\Facades\\Facade::__callStatic('rememberForever', Array)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Helpers\\helpers.php(97): App\\Models\\Setting::get('site_favicon', NULL)
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\storage\\framework\\views\\********************************.php(14): get_setting('site_favicon')
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon11\\\\ww...')
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon11\\\\ww...', Array)
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon11\\\\ww...', Array)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon11\\\\ww...', Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\storage\\framework\\views\\76ced115c407ef364afe8d47ec4b6695.php(263): Illuminate\\View\\View->render()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\laragon11\\\\ww...')
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\laragon11\\\\ww...', Array)
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\laragon11\\\\ww...', Array)
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\laragon11\\\\ww...', Array)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(192): Illuminate\\View\\View->getContents()
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Illuminate\\View\\View->renderContents()
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(79): Illuminate\\View\\View->render()
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\AdminMiddleware.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Middleware\\CheckMaintenanceMode.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#40 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#48 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#49 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#50 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#51 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#71 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(607): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#72 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Testing\\Concerns\\MakesHttpRequests.php(368): Illuminate\\Foundation\\Testing\\TestCase->call('GET', 'http://localhos...', Array, Array, Array, Array)
#73 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\tests\\Feature\\Admin\\AdminDashboardIntegrationTest.php(57): Illuminate\\Foundation\\Testing\\TestCase->get('http://localhos...')
#74 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseMethodFactory.php(168): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->{closure}()
#75 [internal function]: P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Factories\\{closure}()
#76 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): call_user_func_array(Object(Closure), Array)
#77 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Support\\ExceptionTrace.php(26): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->Pest\\Concerns\\{closure}()
#78 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(419): Pest\\Support\\ExceptionTrace::ensure(Object(Closure))
#79 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Concerns\\Testable.php(321): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__callClosure(Object(Closure), Array)
#80 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Factories\\TestCaseFactory.php(169) : eval()'d code(35): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__runTest(Object(Closure))
#81 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(1240): P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest->__pest_evaluable__Admin_Dashboard_Integration__→__Dashboard_Statistics__→_it_caches_dashboard_statistics()
#82 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(514): PHPUnit\\Framework\\TestCase->runTest()
#83 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestRunner\\TestRunner.php(87): PHPUnit\\Framework\\TestCase->runBare()
#84 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestCase.php(361): PHPUnit\\Framework\\TestRunner->run(Object(P\\Tests\\Feature\\Admin\\AdminDashboardIntegrationTest))
#85 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestCase->run()
#86 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#87 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\Framework\\TestSuite.php(369): PHPUnit\\Framework\\TestSuite->run()
#88 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\TestRunner.php(64): PHPUnit\\Framework\\TestSuite->run()
#89 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\phpunit\\phpunit\\src\\TextUI\\Application.php(210): PHPUnit\\TextUI\\TestRunner->run(Object(PHPUnit\\TextUI\\Configuration\\Configuration), Object(PHPUnit\\Runner\\ResultCache\\DefaultResultCache), Object(PHPUnit\\Framework\\TestSuite))
#90 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\src\\Kernel.php(103): PHPUnit\\TextUI\\Application->run(Array)
#91 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(184): Pest\\Kernel->handle(Array, Array)
#92 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\pestphp\\pest\\bin\\pest(192): {closure}()
#93 {main}
"} 
[2025-07-31 05:52:23] production.ERROR: syntax error, unexpected token "public" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\Admin\\LandingPageController.php:98)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 183)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 05:53:02] production.ERROR: syntax error, unexpected token "public" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\Admin\\LandingPageController.php:99)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 183)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 05:53:49] production.ERROR: Class "App\Http\Controllers\Admin\WebhookMonitoringController" does not exist {"exception":"[object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Admin\\WebhookMonitoringController\" does not exist at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php:235)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(235): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(149): Illuminate\\Foundation\\Console\\RouteListCommand->isVendorRoute(Object(Illuminate\\Routing\\Route))
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#3 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 253)
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#8 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#22 {main}
"} 
[2025-07-31 05:54:37] production.ERROR: syntax error, unexpected token "private" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"private\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:299)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 05:56:48] production.ERROR: syntax error, unexpected token "private" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"private\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:299)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 05:59:02] production.ERROR: syntax error, unexpected token "public", expecting end of file {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\", expecting end of file at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:733)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 05:59:51] production.ERROR: syntax error, unexpected token "public" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:929)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 06:02:31] production.ERROR: syntax error, unexpected token "public" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"public\" at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:929)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 06:04:34] production.ERROR: Unclosed '(' on line 859 does not match ']' {"exception":"[object] (ParseError(code: 0): Unclosed '(' on line 859 does not match ']' at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:877)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 06:06:57] production.ERROR: Unclosed '(' on line 859 does not match ']' {"exception":"[object] (ParseError(code: 0): Unclosed '(' on line 859 does not match ']' at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:877)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 06:18:59] production.ERROR: Unclosed '(' on line 870 does not match ']' {"exception":"[object] (ParseError(code: 0): Unclosed '(' on line 870 does not match ']' at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:888)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
[2025-07-31 06:19:18] production.ERROR: Unclosed '(' on line 870 does not match ']' {"exception":"[object] (ParseError(code: 0): Unclosed '(' on line 870 does not match ']' at C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\app\\Http\\Controllers\\User\\PaymentController.php:888)
[stacktrace]
#0 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('C:\\\\laragon11\\\\ww...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1117): is_a('App\\\\Http\\\\Contro...', 'Illuminate\\\\Rout...', true)
#3 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1054): Illuminate\\Routing\\Route->controllerMiddleware()
#4 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Routing\\Route->gatherMiddleware()
#5 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(211): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#6 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(148): Illuminate\\Foundation\\Console\\RouteListCommand->getMiddleware(Object(Illuminate\\Routing\\Route))
#7 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(118): Illuminate\\Foundation\\Console\\RouteListCommand->getRouteInformation(Object(Illuminate\\Routing\\Route))
#8 [internal function]: Illuminate\\Foundation\\Console\\RouteListCommand->Illuminate\\Foundation\\Console\\{closure}(Object(Illuminate\\Routing\\Route), 291)
#9 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#10 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#11 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(117): Illuminate\\Support\\Collection->map(Object(Closure))
#12 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\RouteListCommand.php(103): Illuminate\\Foundation\\Console\\RouteListCommand->getRoutes()
#13 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\RouteListCommand->handle()
#14 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#19 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Foundation\\Console\\RouteListCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon11\\www\\pincode-new-1-without-daisy-ui\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}
"} 
