<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Order;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index()
    {
        $payments = Payment::with(['order.user', 'order.plan'])
            ->latest()
            ->paginate(10);

        return view('admin.payments.index', compact('payments'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['order.user', 'order.plan']);
        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Update payment status (e.g., mark as refunded).
     */
    public function update(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'payment_status' => 'required|in:' . implode(',', [
                Payment::STATUS_COMPLETED,
                Payment::STATUS_REFUNDED,
                Payment::STATUS_FAILED
            ])
        ]);

        $payment->update([
            'payment_status' => $validated['payment_status']
        ]);

        // If payment is refunded, update the associated order status
        if ($validated['payment_status'] === Payment::STATUS_REFUNDED) {
            $payment->order->update([
                'status' => Order::STATUS_REFUNDED
            ]);
        }

        return redirect()->route('admin.payments.show', $payment)
            ->with('success', 'Payment status updated successfully.');
    }
} 