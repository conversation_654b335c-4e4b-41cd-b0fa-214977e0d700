<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\PaymentProof;
use App\Models\Order;
use App\Services\Payment\PaymentGatewayManager;
use App\Services\Payment\QRBankTransferService;
use App\Services\Payment\PaymentGatewayFactory;
use App\Services\Payment\FileUploadSecurityService;
use App\Services\Payment\PaymentValidationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index()
    {
        $payments = Payment::with(['order.user', 'order.plan'])
            ->latest()
            ->paginate(10);

        return view('admin.payments.index', compact('payments'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['order.user', 'order.plan']);
        return view('admin.payments.show', compact('payment'));
    }

    /**
     * Update payment status (e.g., mark as refunded).
     */
    public function update(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'payment_status' => 'required|in:' . implode(',', [
                Payment::STATUS_COMPLETED,
                Payment::STATUS_REFUNDED,
                Payment::STATUS_FAILED
            ])
        ]);

        $payment->update([
            'payment_status' => $validated['payment_status']
        ]);

        // If payment is refunded, update the associated order status
        if ($validated['payment_status'] === Payment::STATUS_REFUNDED) {
            $payment->order->update([
                'status' => Order::STATUS_REFUNDED
            ]);
        }

        return redirect()->route('admin.payments.show', $payment)
            ->with('success', 'Payment status updated successfully.');
    }

    /**
     * Display pending QR payment verifications.
     */
    public function pendingVerifications()
    {
        $pendingPayments = Payment::with(['order.user', 'order.plan', 'paymentProofs'])
            ->where('payment_method', 'qr_bank_transfer')
            ->where('payment_status', Payment::STATUS_PENDING)
            ->whereHas('paymentProofs', function ($query) {
                $query->where('verification_status', PaymentProof::STATUS_PENDING);
            })
            ->latest()
            ->paginate(10);

        return view('admin.payment-verification.index', compact('pendingPayments'));
    }

    /**
     * Show payment proof details for verification.
     */
    public function showVerification(Payment $payment)
    {
        $payment->load(['order.user', 'order.plan', 'paymentProofs']);
        
        // Ensure this is a QR payment with proof
        if ($payment->payment_method !== 'qr_bank_transfer' || $payment->paymentProofs->isEmpty()) {
            return redirect()->route('admin.payments.pending-verifications')
                ->with('error', 'Payment proof not found or invalid payment method.');
        }

        return view('admin.payment-verification.show', compact('payment'));
    }

    /**
     * Approve QR payment after verification.
     */
    public function approvePayment(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'admin_notes' => 'nullable|string|max:1000'
        ]);

        try {
            DB::beginTransaction();

            // Update payment status
            $payment->update([
                'payment_status' => Payment::STATUS_COMPLETED,
                'verified_at' => now(),
                'verified_by' => Auth::id()
            ]);

            // Update payment proof status
            $payment->paymentProofs()->update([
                'verification_status' => PaymentProof::STATUS_APPROVED,
                'admin_notes' => $validated['admin_notes'] ?? null,
                'verified_at' => now(),
                'verified_by' => Auth::id()
            ]);

            // Update order status
            $payment->order->update([
                'status' => Order::STATUS_COMPLETED
            ]);

            // Activate the plan if it's a plan purchase
            if ($payment->order->plan) {
                // Add plan activation logic here if needed
                Log::info('Plan activated for payment', [
                    'payment_id' => $payment->id,
                    'order_id' => $payment->order->id,
                    'plan_id' => $payment->order->plan->id
                ]);
            }

            DB::commit();

            Log::info('QR payment approved by admin', [
                'payment_id' => $payment->id,
                'admin_id' => Auth::id(),
                'admin_notes' => $validated['admin_notes'] ?? null
            ]);

            return redirect()->route('admin.payments.pending-verifications')
                ->with('success', 'Payment approved successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to approve QR payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to approve payment. Please try again.');
        }
    }

    /**
     * Reject QR payment after verification.
     */
    public function rejectPayment(Request $request, Payment $payment)
    {
        $validated = $request->validate([
            'admin_notes' => 'required|string|max:1000',
            'rejection_reason' => 'required|string|in:invalid_proof,insufficient_amount,duplicate_payment,other'
        ]);

        try {
            DB::beginTransaction();

            // Update payment status
            $payment->update([
                'payment_status' => Payment::STATUS_FAILED,
                'verified_at' => now(),
                'verified_by' => Auth::id()
            ]);

            // Update payment proof status
            $payment->paymentProofs()->update([
                'verification_status' => PaymentProof::STATUS_REJECTED,
                'admin_notes' => $validated['admin_notes'],
                'rejection_reason' => $validated['rejection_reason'],
                'verified_at' => now(),
                'verified_by' => Auth::id()
            ]);

            // Update order status
            $payment->order->update([
                'status' => Order::STATUS_FAILED
            ]);

            DB::commit();

            Log::info('QR payment rejected by admin', [
                'payment_id' => $payment->id,
                'admin_id' => Auth::id(),
                'rejection_reason' => $validated['rejection_reason'],
                'admin_notes' => $validated['admin_notes']
            ]);

            return redirect()->route('admin.payments.pending-verifications')
                ->with('success', 'Payment rejected successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to reject QR payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to reject payment. Please try again.');
        }
    }

    /**
     * Download payment proof file securely.
     */
    public function downloadProof(PaymentProof $paymentProof, FileUploadSecurityService $fileSecurityService)
    {
        try {
            // Validate admin access to this file
            $userId = $paymentProof->payment->order->user_id;
            
            // Use secure file retrieval
            $fileResult = $fileSecurityService->securelyRetrieveFile($paymentProof->file_path, $userId);
            
            if (!$fileResult['success']) {
                Log::warning('Admin attempted to download non-existent payment proof', [
                    'admin_id' => Auth::id(),
                    'payment_proof_id' => $paymentProof->id,
                    'file_path' => $paymentProof->file_path,
                    'error' => $fileResult['error']
                ]);
                
                return back()->with('error', 'Payment proof file not found or access denied.');
            }

            // Log the download for audit purposes
            Log::info('Admin downloaded payment proof', [
                'admin_id' => Auth::id(),
                'payment_proof_id' => $paymentProof->id,
                'payment_id' => $paymentProof->payment_id,
                'file_path' => $paymentProof->file_path,
                'user_id' => $userId
            ]);

            return Storage::disk('private')->download(
                $paymentProof->file_path,
                $paymentProof->file_name
            );

        } catch (\Exception $e) {
            Log::error('Failed to download payment proof', [
                'admin_id' => Auth::id(),
                'payment_proof_id' => $paymentProof->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to download payment proof file.');
        }
    }

    /**
     * View payment proof file securely (for images).
     */
    public function viewProof(PaymentProof $paymentProof, FileUploadSecurityService $fileSecurityService)
    {
        try {
            // Validate admin access to this file
            $userId = $paymentProof->payment->order->user_id;
            
            // Use secure file retrieval
            $fileResult = $fileSecurityService->securelyRetrieveFile($paymentProof->file_path, $userId);
            
            if (!$fileResult['success']) {
                return response()->json(['error' => 'File not found or access denied'], 404);
            }

            // Only allow viewing of image files
            if (!str_starts_with($paymentProof->mime_type, 'image/')) {
                return response()->json(['error' => 'File type not supported for viewing'], 400);
            }

            // Log the view for audit purposes
            Log::info('Admin viewed payment proof', [
                'admin_id' => Auth::id(),
                'payment_proof_id' => $paymentProof->id,
                'payment_id' => $paymentProof->payment_id,
                'file_path' => $paymentProof->file_path,
                'user_id' => $userId
            ]);

            $fileContent = Storage::disk('private')->get($paymentProof->file_path);
            
            return response($fileContent)
                ->header('Content-Type', $paymentProof->mime_type)
                ->header('Content-Disposition', 'inline; filename="' . $paymentProof->file_name . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            Log::error('Failed to view payment proof', [
                'admin_id' => Auth::id(),
                'payment_proof_id' => $paymentProof->id,
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to load payment proof'], 500);
        }
    }

    /**
     * Get payment verification statistics.
     */
    public function verificationStats()
    {
        $stats = [
            'pending' => Payment::where('payment_method', 'qr_bank_transfer')
                ->where('payment_status', Payment::STATUS_PENDING)
                ->count(),
            'approved_today' => Payment::where('payment_method', 'qr_bank_transfer')
                ->where('payment_status', Payment::STATUS_COMPLETED)
                ->whereDate('verified_at', today())
                ->count(),
            'rejected_today' => Payment::where('payment_method', 'qr_bank_transfer')
                ->where('payment_status', Payment::STATUS_FAILED)
                ->whereDate('verified_at', today())
                ->count(),
            'total_verified' => Payment::where('payment_method', 'qr_bank_transfer')
                ->whereIn('payment_status', [Payment::STATUS_COMPLETED, Payment::STATUS_FAILED])
                ->whereNotNull('verified_at')
                ->count()
        ];

        return response()->json($stats);
    }

    /**
     * Get payment security statistics and monitoring data.
     */
    public function securityStats(FileUploadSecurityService $fileSecurityService)
    {
        try {
            // Get file upload statistics
            $uploadStats = $fileSecurityService->getUploadStatistics();

            // Get recent security events from logs
            $securityEvents = $this->getRecentSecurityEvents();

            // Get rate limiting statistics
            $rateLimitStats = $this->getRateLimitingStats();

            $stats = [
                'file_uploads' => $uploadStats,
                'security_events' => $securityEvents,
                'rate_limiting' => $rateLimitStats,
                'system_health' => [
                    'storage_accessible' => Storage::disk('private')->exists('payment_proofs'),
                    'config_loaded' => config('payment.security') !== null,
                    'last_updated' => now()->toISOString()
                ]
            ];

            return response()->json($stats);

        } catch (\Exception $e) {
            Log::error('Failed to get payment security stats', [
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to load security statistics'], 500);
        }
    }

    /**
     * Clean up old payment proof files.
     */
    public function cleanupOldFiles(Request $request, FileUploadSecurityService $fileSecurityService)
    {
        $validated = $request->validate([
            'days_old' => 'required|integer|min:30|max:365'
        ]);

        try {
            $deletedCount = $fileSecurityService->cleanupOldFiles($validated['days_old']);

            Log::info('Admin initiated payment file cleanup', [
                'admin_id' => Auth::id(),
                'days_old' => $validated['days_old'],
                'deleted_count' => $deletedCount
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully cleaned up {$deletedCount} old files",
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old payment files', [
                'admin_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json(['error' => 'Failed to cleanup old files'], 500);
        }
    }

    /**
     * Get recent security events from logs.
     */
    private function getRecentSecurityEvents(): array
    {
        // This is a simplified implementation
        // In production, you might want to use a proper log aggregation service
        return [
            'malware_detections' => 0, // Would be populated from actual log analysis
            'suspicious_uploads' => 0,
            'rate_limit_violations' => 0,
            'unauthorized_access_attempts' => 0,
            'last_24_hours' => [
                'total_events' => 0,
                'critical_events' => 0
            ]
        ];
    }

    /**
     * Get rate limiting statistics.
     */
    private function getRateLimitingStats(): array
    {
        // This would typically come from your cache/rate limiter
        return [
            'active_limits' => 0,
            'blocked_requests_today' => 0,
            'top_limited_operations' => []
        ];
    }
} 