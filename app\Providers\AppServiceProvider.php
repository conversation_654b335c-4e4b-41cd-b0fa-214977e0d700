<?php

namespace App\Providers;

use App\Http\Middleware\CheckMaintenanceMode;
use App\View\Composers\SidebarComposer;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {


        if (config('app.env') === 'production') {
            URL::forceScheme('https');
        }
        
        // Remove index.php from URLs
        if (request()->server('HTTP_HOST')) {
            URL::forceRootUrl(config('app.url'));
        }

        // Register view composers
        View::composer('pincodes.partials.sidebar', SidebarComposer::class);

        // Define rate limiter for API routes
        RateLimiter::for('api', function (Request $request) {
            $rateLimit = config('app.api_rate_limit', 60);  // Default 60 requests per minute
            return Limit::perMinute($rateLimit)->by($request->user()?->id ?: $request->ip());
        });

        $this->app['router']->aliasMiddleware('maintenance', CheckMaintenanceMode::class);

        // Publish typography JS file if it doesn't exist
        // $jsPath = public_path('js/typography-settings.js');
        // $resourceJsPath = resource_path('js/typography-settings.js');

        // if (!file_exists($jsPath) && file_exists($resourceJsPath)) {
        //     $directory = dirname($jsPath);
        //     if (!is_dir($directory)) {
        //         $permissions = config('app.file_permissions', 0755);
        //         mkdir($directory, $permissions, true);
        //     }
        //     copy($resourceJsPath, $jsPath);
        // }

    }
}
